package com.example.medicalpoc

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.medicalpoc.models.DeviceType
import com.example.medicalpoc.ui.DeviceTypeAdapter

class DeviceSelectionActivity : AppCompatActivity() {
    
    private lateinit var deviceTypeRecyclerView: RecyclerView
    private lateinit var deviceTypeAdapter: DeviceTypeAdapter
    
    companion object {
        const val EXTRA_SELECTED_DEVICE_TYPE = "selected_device_type"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_selection)
        
        setupUI()
        setupDeviceTypeList()
    }
    
    private fun setupUI() {
        supportActionBar?.title = getString(R.string.select_device_type)
        
        deviceTypeRecyclerView = findViewById(R.id.deviceTypeRecyclerView)
        deviceTypeRecyclerView.layoutManager = GridLayoutManager(this, 2)
    }
    
    private fun setupDeviceTypeList() {
        val deviceTypes = DeviceType.getAllDeviceTypes()
        
        deviceTypeAdapter = DeviceTypeAdapter(deviceTypes) { selectedDeviceType ->
            onDeviceTypeSelected(selectedDeviceType)
        }
        
        deviceTypeRecyclerView.adapter = deviceTypeAdapter
    }
    
    private fun onDeviceTypeSelected(deviceType: DeviceType) {
        val intent = Intent(this, MainActivity::class.java).apply {
            putExtra(EXTRA_SELECTED_DEVICE_TYPE, deviceType.id)
        }
        startActivity(intent)
        finish()
    }
}
