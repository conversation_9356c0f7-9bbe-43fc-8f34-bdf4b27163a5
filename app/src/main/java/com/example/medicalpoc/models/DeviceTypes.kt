package com.example.medicalpoc.models

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.example.medicalpoc.R

/**
 * Enum representing different types of medical devices supported by the app
 */
enum class DeviceType(
    val id: String,
    @StringRes val displayName: Int,
    @StringRes val description: Int,
    @DrawableRes val icon: Int,
    val measurementUnit: String,
    val measurementRange: ClosedFloatingPointRange<Float>,
    val serviceUUIDs: List<String> = emptyList()
) {
    THERMOMETER(
        id = "thermometer",
        displayName = R.string.device_thermometer,
        description = R.string.device_thermometer_desc,
        icon = R.drawable.ic_thermometer,
        measurementUnit = "°C",
        measurementRange = 30.0f..45.0f,
        serviceUUIDs = listOf(
            "00001809-0000-1000-8000-00805f9b34fb", // Health Thermometer Service
            "5833ff01-9b8b-5191-6142-22a4536ef123"  // Custom thermometer service
        )
    ),
    
    PULSE_OXIMETER(
        id = "pulse_oximeter",
        displayName = R.string.device_pulse_oximeter,
        description = R.string.device_pulse_oximeter_desc,
        icon = R.drawable.ic_pulse_oximeter,
        measurementUnit = "%",
        measurementRange = 70.0f..100.0f,
        serviceUUIDs = listOf(
            "00001822-0000-1000-8000-00805f9b34fb", // Pulse Oximeter Service
            "0000180d-0000-1000-8000-00805f9b34fb"  // Heart Rate Service
        )
    ),
    
    HBCHECK_BIOSENSE(
        id = "hbcheck_biosense",
        displayName = R.string.device_hbcheck,
        description = R.string.device_hbcheck_desc,
        icon = R.drawable.ic_hemoglobin,
        measurementUnit = "g/dL",
        measurementRange = 5.0f..20.0f,
        serviceUUIDs = listOf(
            "0000180f-0000-1000-8000-00805f9b34fb", // Battery Service (common in medical devices)
            "6e400001-b5a3-f393-e0a9-e50e24dcca9e"  // Nordic UART Service (common for custom protocols)
        )
    ),
    
    A1_CHECK_ANALYZER(
        id = "a1_check",
        displayName = R.string.device_a1_check,
        description = R.string.device_a1_check_desc,
        icon = R.drawable.ic_glucose,
        measurementUnit = "%",
        measurementRange = 4.0f..15.0f,
        serviceUUIDs = listOf(
            "00001808-0000-1000-8000-00805f9b34fb", // Glucose Service
            "6e400001-b5a3-f393-e0a9-e50e24dcca9e"  // Nordic UART Service
        )
    ),
    
    BLOOD_LIPID_METER(
        id = "blood_lipid",
        displayName = R.string.device_blood_lipid,
        description = R.string.device_blood_lipid_desc,
        icon = R.drawable.ic_lipid,
        measurementUnit = "mg/dL",
        measurementRange = 100.0f..400.0f,
        serviceUUIDs = listOf(
            "6e400001-b5a3-f393-e0a9-e50e24dcca9e", // Nordic UART Service
            "0000180f-0000-1000-8000-00805f9b34fb"  // Battery Service
        )
    ),
    
    DIGITAL_STETHOSCOPE(
        id = "stethoscope",
        displayName = R.string.device_stethoscope,
        description = R.string.device_stethoscope_desc,
        icon = R.drawable.ic_stethoscope,
        measurementUnit = "BPM",
        measurementRange = 40.0f..200.0f,
        serviceUUIDs = listOf(
            "0000180d-0000-1000-8000-00805f9b34fb", // Heart Rate Service
            "6e400001-b5a3-f393-e0a9-e50e24dcca9e"  // Nordic UART Service
        )
    ),
    
    BLOOD_PRESSURE_MONITOR(
        id = "blood_pressure",
        displayName = R.string.device_blood_pressure,
        description = R.string.device_blood_pressure_desc,
        icon = R.drawable.ic_blood_pressure,
        measurementUnit = "mmHg",
        measurementRange = 60.0f..250.0f,
        serviceUUIDs = listOf(
            "00001810-0000-1000-8000-00805f9b34fb", // Blood Pressure Service
            "0000180f-0000-1000-8000-00805f9b34fb"  // Battery Service
        )
    ),
    
    SPIROMETER(
        id = "spirometer",
        displayName = R.string.device_spirometer,
        description = R.string.device_spirometer_desc,
        icon = R.drawable.ic_spirometer,
        measurementUnit = "L",
        measurementRange = 1.0f..8.0f,
        serviceUUIDs = listOf(
            "6e400001-b5a3-f393-e0a9-e50e24dcca9e", // Nordic UART Service
            "0000180f-0000-1000-8000-00805f9b34fb"  // Battery Service
        )
    );
    
    companion object {
        fun fromId(id: String): DeviceType? = values().find { it.id == id }
        
        fun getAllDeviceTypes(): List<DeviceType> = values().toList()
    }
}

/**
 * Base class for all medical device measurements
 */
sealed class MedicalMeasurement {
    abstract val deviceType: DeviceType
    abstract val timestamp: Long
    abstract val deviceName: String?
    abstract val primaryValue: Float
    abstract val unit: String
}

/**
 * Temperature measurement from thermometer
 */
data class TemperatureMeasurement(
    override val deviceType: DeviceType = DeviceType.THERMOMETER,
    override val timestamp: Long,
    override val deviceName: String?,
    override val primaryValue: Float, // Temperature in Celsius
    override val unit: String = "°C",
    val bodyLocation: String? = null // forehead, ear, oral, etc.
) : MedicalMeasurement()

/**
 * Pulse oximeter measurement (SpO2 and heart rate)
 */
data class PulseOximeterMeasurement(
    override val deviceType: DeviceType = DeviceType.PULSE_OXIMETER,
    override val timestamp: Long,
    override val deviceName: String?,
    override val primaryValue: Float, // SpO2 percentage
    override val unit: String = "%",
    val heartRate: Int, // BPM
    val perfusionIndex: Float? = null
) : MedicalMeasurement()

/**
 * Hemoglobin measurement from HbCheck Biosense
 */
data class HemoglobinMeasurement(
    override val deviceType: DeviceType = DeviceType.HBCHECK_BIOSENSE,
    override val timestamp: Long,
    override val deviceName: String?,
    override val primaryValue: Float, // Hemoglobin in g/dL
    override val unit: String = "g/dL",
    val hematocrit: Float? = null
) : MedicalMeasurement()

/**
 * HbA1c measurement from A1 Check analyzer
 */
data class HbA1cMeasurement(
    override val deviceType: DeviceType = DeviceType.A1_CHECK_ANALYZER,
    override val timestamp: Long,
    override val deviceName: String?,
    override val primaryValue: Float, // HbA1c percentage
    override val unit: String = "%",
    val estimatedAverageGlucose: Float? = null // mg/dL
) : MedicalMeasurement()

/**
 * Blood lipid measurement (cholesterol, etc.)
 */
data class BloodLipidMeasurement(
    override val deviceType: DeviceType = DeviceType.BLOOD_LIPID_METER,
    override val timestamp: Long,
    override val deviceName: String?,
    override val primaryValue: Float, // Total cholesterol in mg/dL
    override val unit: String = "mg/dL",
    val hdlCholesterol: Float? = null,
    val ldlCholesterol: Float? = null,
    val triglycerides: Float? = null
) : MedicalMeasurement()

/**
 * Heart rate measurement from digital stethoscope
 */
data class StethoscopeMeasurement(
    override val deviceType: DeviceType = DeviceType.DIGITAL_STETHOSCOPE,
    override val timestamp: Long,
    override val deviceName: String?,
    override val primaryValue: Float, // Heart rate in BPM
    override val unit: String = "BPM",
    val rhythm: String? = null, // regular, irregular, etc.
    val audioRecordingPath: String? = null
) : MedicalMeasurement()

/**
 * Blood pressure measurement
 */
data class BloodPressureMeasurement(
    override val deviceType: DeviceType = DeviceType.BLOOD_PRESSURE_MONITOR,
    override val timestamp: Long,
    override val deviceName: String?,
    override val primaryValue: Float, // Systolic pressure
    override val unit: String = "mmHg",
    val diastolicPressure: Float,
    val meanArterialPressure: Float? = null,
    val heartRate: Int? = null
) : MedicalMeasurement()

/**
 * Spirometer measurement (lung function)
 */
data class SpirometerMeasurement(
    override val deviceType: DeviceType = DeviceType.SPIROMETER,
    override val timestamp: Long,
    override val deviceName: String?,
    override val primaryValue: Float, // FEV1 in liters
    override val unit: String = "L",
    val fvc: Float? = null, // Forced Vital Capacity
    val fev1FvcRatio: Float? = null,
    val peakFlow: Float? = null
) : MedicalMeasurement()
