package com.example.medicalpoc.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothManager
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanFilter
import android.bluetooth.le.ScanSettings
import android.content.Context
import android.content.pm.PackageManager
import android.os.Handler
import android.os.Looper
import android.os.ParcelUuid
import android.util.Log
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import com.example.medicalpoc.models.DeviceType
import java.util.UUID

class BluetoothDeviceScanner(private val context: Context) {
    
    private val bluetoothManager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
    private val bluetoothAdapter: BluetoothAdapter? = bluetoothManager.adapter
    private val bluetoothLeScanner: BluetoothLeScanner? = bluetoothAdapter?.bluetoothLeScanner
    
    private val _scanResults = MutableStateFlow<List<ScanResult>>(emptyList())
    val scanResults: StateFlow<List<ScanResult>> = _scanResults.asStateFlow()
    
    private val _isScanning = MutableStateFlow(false)
    val isScanning: StateFlow<Boolean> = _isScanning.asStateFlow()
    
    private val _scanError = MutableStateFlow<String?>(null)
    val scanError: StateFlow<String?> = _scanError.asStateFlow()
    
    private val scanHandler = Handler(Looper.getMainLooper())
    private val scanTimeout = 10000L // 10 seconds

    // Current device type filter
    private var currentDeviceTypeFilter: DeviceType? = null

    companion object {
        private const val TAG = "BluetoothDeviceScanner"
    }

    private val scanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult) {
            super.onScanResult(callbackType, result)
            
            val currentResults = _scanResults.value.toMutableList()
            val existingIndex = currentResults.indexOfFirst { 
                it.device.address == result.device.address 
            }
            
            if (existingIndex >= 0) {
                currentResults[existingIndex] = result
            } else {
                currentResults.add(result)
            }
            
            _scanResults.value = currentResults

            val deviceName = result.device.name ?: "Unknown"
            val deviceAddress = result.device.address
            val rssi = result.rssi

            // Enhanced logging for debugging
            Log.d(TAG, "Device found: $deviceName ($deviceAddress) RSSI: $rssi")

            // Log service UUIDs if available
            val scanRecord = result.scanRecord
            scanRecord?.serviceUuids?.let { uuids ->
                Log.d(TAG, "Device $deviceName advertises services: ${uuids.joinToString { it.toString() }}")
            }

            // Log manufacturer data if available
            scanRecord?.manufacturerSpecificData?.let { data ->
                if (data.size() > 0) {
                    Log.d(TAG, "Device $deviceName has manufacturer data (${data.size()} entries)")
                }
            }

            Log.d(TAG, "Total devices found: ${currentResults.size}")
        }
        
        override fun onScanFailed(errorCode: Int) {
            super.onScanFailed(errorCode)
            _isScanning.value = false
            _scanError.value = "Scan failed with error code: $errorCode"
            Log.e(TAG, "Scan failed with error code: $errorCode")
        }
    }
    
    fun startScan(deviceType: DeviceType? = null) {
        Log.d(TAG, "startScan called with device type: ${deviceType?.id ?: "null"}")

        if (!hasBluetoothPermissions()) {
            Log.e(TAG, "Missing Bluetooth permissions")
            _scanError.value = "Bluetooth permissions not granted"
            return
        }

        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled) {
            Log.e(TAG, "Bluetooth is not enabled")
            _scanError.value = "Bluetooth is not enabled"
            return
        }

        currentDeviceTypeFilter = deviceType

        if (_isScanning.value) {
            Log.w(TAG, "Scan already in progress")
            return
        }

        Log.d(TAG, "Starting BLE scan for device type: ${deviceType?.id ?: "all"}")
        _scanResults.value = emptyList()
        _scanError.value = null
        _isScanning.value = true

        val scanSettings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
            .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES)
            .build()

        // Create scan filters based on device type
        val scanFilters = createScanFilters(deviceType)
        Log.d(TAG, "Scan filters: ${if (scanFilters == null) "none (scanning all)" else "${scanFilters.size} filters"}")

        try {
            bluetoothLeScanner?.startScan(scanFilters, scanSettings, scanCallback)
            
            // Stop scan after timeout
            scanHandler.postDelayed({
                stopScan()
            }, scanTimeout)
            
            Log.d(TAG, "Started BLE scan")
        } catch (e: SecurityException) {
            _isScanning.value = false
            _scanError.value = "Security exception: ${e.message}"
            Log.e(TAG, "Security exception during scan", e)
        }
    }
    
    fun stopScan() {
        if (!_isScanning.value) {
            return
        }
        
        try {
            bluetoothLeScanner?.stopScan(scanCallback)
            _isScanning.value = false
            scanHandler.removeCallbacksAndMessages(null)
            Log.d(TAG, "Stopped BLE scan")
        } catch (e: SecurityException) {
            _scanError.value = "Security exception: ${e.message}"
            Log.e(TAG, "Security exception during scan stop", e)
        }
    }
    
    fun isBluetoothEnabled(): Boolean {
        return bluetoothAdapter?.isEnabled == true
    }
    
    private fun hasBluetoothPermissions(): Boolean {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED &&
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
        } else {
            ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun createScanFilters(deviceType: DeviceType?): List<ScanFilter>? {
        if (deviceType == null) {
            Log.d(TAG, "No device type specified, scanning for all devices")
            return null
        }

        // For thermometer and pulse oximeter, scan all devices since filtering might miss devices
        if (deviceType == DeviceType.THERMOMETER || deviceType == DeviceType.PULSE_OXIMETER) {
            Log.d(TAG, "${deviceType.id} selected - scanning all devices (no filters)")
            return null
        }

        // For other devices, try to use service UUID filters
        val filters = mutableListOf<ScanFilter>()

        deviceType.serviceUUIDs.forEach { uuidString ->
            try {
                val uuid = UUID.fromString(uuidString)
                val filter = ScanFilter.Builder()
                    .setServiceUuid(ParcelUuid(uuid))
                    .build()
                filters.add(filter)
                Log.d(TAG, "Added scan filter for service UUID: $uuidString")
            } catch (e: IllegalArgumentException) {
                Log.w(TAG, "Invalid UUID format: $uuidString", e)
            }
        }

        // If no valid UUIDs were found, fall back to scanning all devices
        return if (filters.isEmpty()) {
            Log.d(TAG, "No valid service UUIDs found for ${deviceType.id}, scanning all devices")
            null
        } else {
            Log.d(TAG, "Created ${filters.size} scan filters for ${deviceType.id}")
            filters
        }
    }
}
