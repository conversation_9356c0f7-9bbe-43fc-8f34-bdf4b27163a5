package com.example.medicalpoc.bluetooth

import android.Manifest
import android.bluetooth.*
import android.content.Context
import android.content.pm.PackageManager
import android.util.Log
import androidx.annotation.RequiresPermission
import androidx.core.app.ActivityCompat
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import com.example.medicalpoc.models.DeviceType
import com.example.medicalpoc.models.PulseOximeterMeasurement
import java.util.*

class BluetoothConnectionManager(private val context: Context) {

    private var bluetoothGatt: BluetoothGatt? = null
    private var currentDeviceType: DeviceType = DeviceType.THERMOMETER

    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()

    private val _temperatureData = MutableStateFlow<TemperatureReading?>(null)
    val temperatureData: StateFlow<TemperatureReading?> = _temperatureData.asStateFlow()

    private val _pulseOximeterData = MutableStateFlow<PulseOximeterReading?>(null)
    val pulseOximeterData: StateFlow<PulseOximeterReading?> = _pulseOximeterData.asStateFlow()

    private val _connectionError = MutableStateFlow<String?>(null)
    val connectionError: StateFlow<String?> = _connectionError.asStateFlow()
    
    // Common UUIDs for medical devices
    companion object {
        private const val TAG = "BluetoothConnectionManager"

        // Health Thermometer Service UUID
        val HEALTH_THERMOMETER_SERVICE_UUID: UUID = UUID.fromString("00001809-0000-1000-8000-00805f9b34fb")

        // Temperature Measurement Characteristic UUID
        val TEMPERATURE_MEASUREMENT_CHAR_UUID: UUID = UUID.fromString("00002a1c-0000-1000-8000-00805f9b34fb")

        // Pulse Oximeter Service UUID
        val PULSE_OXIMETER_SERVICE_UUID: UUID = UUID.fromString("00001822-0000-1000-8000-00805f9b34fb")

        // PLX Spot-Check Measurement Characteristic UUID
        val PLX_SPOT_CHECK_MEASUREMENT_CHAR_UUID: UUID = UUID.fromString("00002a5e-0000-1000-8000-00805f9b34fb")

        // PLX Continuous Measurement Characteristic UUID
        val PLX_CONTINUOUS_MEASUREMENT_CHAR_UUID: UUID = UUID.fromString("00002a5f-0000-1000-8000-00805f9b34fb")

        // Client Characteristic Configuration Descriptor UUID
        val CLIENT_CHARACTERISTIC_CONFIG_UUID: UUID = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
    }
    
    private val gattCallback = object : BluetoothGattCallback() {
        @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
        override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
            super.onConnectionStateChange(gatt, status, newState)
            
            when (newState) {
                BluetoothProfile.STATE_CONNECTED -> {
                    _connectionState.value = ConnectionState.CONNECTED
                    _connectionError.value = null
                    Log.d(TAG, "Connected to GATT server")
                    
                    // Discover services
                    try {
                        gatt?.discoverServices()
                    } catch (e: SecurityException) {
                        _connectionError.value = "Security exception during service discovery: ${e.message}"
                        Log.e(TAG, "Security exception during service discovery", e)
                    }
                }
                BluetoothProfile.STATE_DISCONNECTED -> {
                    _connectionState.value = ConnectionState.DISCONNECTED
                    Log.d(TAG, "Disconnected from GATT server")
                    bluetoothGatt?.close()
                    bluetoothGatt = null
                }
                BluetoothProfile.STATE_CONNECTING -> {
                    _connectionState.value = ConnectionState.CONNECTING
                    Log.d(TAG, "Connecting to GATT server")
                }
            }
        }
        
        override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
            super.onServicesDiscovered(gatt, status)

            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.d(TAG, "Services discovered")

                // Log all available services and characteristics for debugging
                gatt?.services?.forEach { service ->
                    Log.d(TAG, "Service found: ${service.uuid}")
                    service.characteristics.forEach { characteristic ->
                        Log.d(TAG, "  Characteristic: ${characteristic.uuid}, Properties: ${characteristic.properties}")
                        characteristic.descriptors.forEach { descriptor ->
                            Log.d(TAG, "    Descriptor: ${descriptor.uuid}")
                        }
                    }
                }

                setupNotifications(gatt)
            } else {
                _connectionError.value = "Service discovery failed with status: $status"
                Log.e(TAG, "Service discovery failed with status: $status")
            }
        }
        
        override fun onCharacteristicChanged(gatt: BluetoothGatt?, characteristic: BluetoothGattCharacteristic?) {
            super.onCharacteristicChanged(gatt, characteristic)

            characteristic?.let { char ->
                Log.d(TAG, "Characteristic changed: ${char.uuid}")
                Log.d(TAG, "Data received: ${char.value?.joinToString(" ") { "%02x".format(it) }}")

                when {
                    // Temperature measurement characteristics
                    char.uuid == TEMPERATURE_MEASUREMENT_CHAR_UUID -> {
                        val temperatureReading = parseTemperatureData(char.value)
                        _temperatureData.value = temperatureReading
                        Log.d(TAG, "Temperature reading: ${temperatureReading.temperature}°${temperatureReading.unit}")
                    }

                    // Pulse oximeter measurement characteristics
                    char.uuid == PLX_SPOT_CHECK_MEASUREMENT_CHAR_UUID ||
                    char.uuid == PLX_CONTINUOUS_MEASUREMENT_CHAR_UUID -> {
                        val pulseOximeterReading = parsePulseOximeterData(char.value)
                        _pulseOximeterData.value = pulseOximeterReading
                        Log.d(TAG, "Pulse oximeter reading: SpO2=${pulseOximeterReading.spO2}%, HR=${pulseOximeterReading.heartRate}bpm")
                    }

                    else -> {
                        // Try to parse data based on current device type
                        Log.d(TAG, "Attempting to parse data from non-standard characteristic for device type: ${currentDeviceType.id}")
                        try {
                            when (currentDeviceType) {
                                DeviceType.THERMOMETER -> {
                                    val temperatureReading = parseTemperatureData(char.value)
                                    _temperatureData.value = temperatureReading
                                    Log.d(TAG, "Temperature reading from non-standard characteristic: ${temperatureReading.temperature}°${temperatureReading.unit}")
                                }
                                DeviceType.PULSE_OXIMETER -> {
                                    val pulseOximeterReading = parsePulseOximeterData(char.value)
                                    _pulseOximeterData.value = pulseOximeterReading
                                    Log.d(TAG, "Pulse oximeter reading from non-standard characteristic: SpO2=${pulseOximeterReading.spO2}%, HR=${pulseOximeterReading.heartRate}bpm")
                                }
                                else -> {
                                    Log.d(TAG, "No parser available for device type: ${currentDeviceType.id}")
                                }
                            }
                        } catch (e: Exception) {
                            Log.d(TAG, "Failed to parse data from characteristic: ${e.message}")
                        }
                    }
                }
            }
        }
        
        override fun onDescriptorWrite(gatt: BluetoothGatt?, descriptor: BluetoothGattDescriptor?, status: Int) {
            super.onDescriptorWrite(gatt, descriptor, status)
            
            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.d(TAG, "Descriptor write successful")
            } else {
                _connectionError.value = "Descriptor write failed with status: $status"
                Log.e(TAG, "Descriptor write failed with status: $status")
            }
        }
    }

    fun setDeviceType(deviceType: DeviceType) {
        currentDeviceType = deviceType
        Log.d(TAG, "Device type set to: ${deviceType.id}")
    }

    fun connectToDevice(device: BluetoothDevice) {
        if (!hasBluetoothPermissions()) {
            _connectionError.value = "Bluetooth permissions not granted"
            return
        }
        
        _connectionError.value = null
        
        try {
            bluetoothGatt = device.connectGatt(context, false, gattCallback)
        } catch (e: SecurityException) {
            _connectionError.value = "Security exception during connection: ${e.message}"
            Log.e(TAG, "Security exception during connection", e)
        }
    }
    
    fun disconnect() {
        try {
            bluetoothGatt?.disconnect()
        } catch (e: SecurityException) {
            _connectionError.value = "Security exception during disconnection: ${e.message}"
            Log.e(TAG, "Security exception during disconnection", e)
        }
    }
    
    private fun setupNotifications(gatt: BluetoothGatt?) {
        var notificationSetup = false

        // First try the standard Health Thermometer Service
        val service = gatt?.getService(HEALTH_THERMOMETER_SERVICE_UUID)
        val characteristic = service?.getCharacteristic(TEMPERATURE_MEASUREMENT_CHAR_UUID)

        if (characteristic != null) {
            Log.d(TAG, "Found standard Health Thermometer characteristic")
            if (enableNotificationForCharacteristic(gatt, characteristic)) {
                notificationSetup = true
            }
        }

        // If standard service not found, try to enable notifications on all available characteristics
        if (!notificationSetup) {
            Log.d(TAG, "Standard Health Thermometer service not found, trying all characteristics")
            gatt?.services?.forEach { service ->
                service.characteristics.forEach { char ->
                    val properties = char.properties
                    if ((properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0 ||
                        (properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                        Log.d(TAG, "Attempting to enable notifications for characteristic: ${char.uuid}")
                        if (enableNotificationForCharacteristic(gatt, char)) {
                            notificationSetup = true
                        }
                    }
                }
            }
        }

        if (!notificationSetup) {
            _connectionError.value = "No notifiable characteristics found"
            Log.e(TAG, "No notifiable characteristics found")
        }
    }

    private fun enableNotificationForCharacteristic(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic): Boolean {
        return try {
            // Enable notifications
            val success = gatt.setCharacteristicNotification(characteristic, true)
            Log.d(TAG, "setCharacteristicNotification result: $success for ${characteristic.uuid}")

            // Write to descriptor to enable notifications
            val descriptor = characteristic.getDescriptor(CLIENT_CHARACTERISTIC_CONFIG_UUID)
            if (descriptor != null) {
                val value = if ((characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                    BluetoothGattDescriptor.ENABLE_INDICATION_VALUE
                } else {
                    BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                }
                descriptor.value = value
                val writeResult = gatt.writeDescriptor(descriptor)
                Log.d(TAG, "writeDescriptor result: $writeResult for ${characteristic.uuid}")
                success && writeResult
            } else {
                Log.d(TAG, "No descriptor found for characteristic: ${characteristic.uuid}")
                success
            }
        } catch (e: SecurityException) {
            _connectionError.value = "Security exception during notification setup: ${e.message}"
            Log.e(TAG, "Security exception during notification setup", e)
            false
        }
    }

    private fun parsePulseOximeterData(data: ByteArray): PulseOximeterReading {
        Log.d(TAG, "Parsing pulse oximeter data: ${data.joinToString(" ") { "%02x".format(it) }} (${data.size} bytes)")

        if (data.isEmpty()) {
            throw IllegalArgumentException("Empty data array")
        }

        // Try different parsing strategies for pulse oximeter data
        return when {
            data.size >= 4 -> parseStandardPulseOximeterData(data)
            data.size >= 3 -> parseSimplePulseOximeterData(data)
            else -> throw IllegalArgumentException("Pulse oximeter data too short: ${data.size} bytes")
        }
    }

    private fun parseStandardPulseOximeterData(data: ByteArray): PulseOximeterReading {
        Log.d(TAG, "=== STANDARD PULSE OXIMETER PARSING ===")
        Log.d(TAG, "Raw data: ${data.joinToString(" ") { "%02x".format(it) }}")

        // Standard Bluetooth pulse oximeter format (IEEE 11073-20601)
        val flags = data[0].toInt() and 0xFF
        Log.d(TAG, "Flags: 0x${flags.toString(16)}")

        var spO2: Float = 0f
        var heartRate: Int = 0
        var perfusionIndex: Float? = null
        var offset = 1

        // SpO2 value (usually 16-bit)
        if (data.size > offset + 1) {
            spO2 = ((data[offset + 1].toInt() and 0xFF) shl 8 or (data[offset].toInt() and 0xFF)).toFloat() / 100f
            offset += 2
            Log.d(TAG, "SpO2 from standard format: $spO2%")
        }

        // Heart rate (usually 16-bit)
        if (data.size > offset + 1) {
            heartRate = (data[offset + 1].toInt() and 0xFF) shl 8 or (data[offset].toInt() and 0xFF)
            offset += 2
            Log.d(TAG, "Heart rate from standard format: ${heartRate}bpm")
        }

        // Perfusion Index if available
        if (data.size > offset + 1) {
            val piRaw = (data[offset + 1].toInt() and 0xFF) shl 8 or (data[offset].toInt() and 0xFF)
            perfusionIndex = piRaw.toFloat() / 100f
            Log.d(TAG, "Perfusion Index: $perfusionIndex")
        }

        // Validate reasonable ranges
        if (spO2 < 70f || spO2 > 100f) {
            Log.w(TAG, "SpO2 value out of range: $spO2%, trying alternative parsing")
            return parseAlternativePulseOximeterData(data)
        }

        if (heartRate < 30 || heartRate > 250) {
            Log.w(TAG, "Heart rate out of range: ${heartRate}bpm, trying alternative parsing")
            return parseAlternativePulseOximeterData(data)
        }

        return PulseOximeterReading(
            spO2 = spO2,
            heartRate = heartRate,
            perfusionIndex = perfusionIndex,
            timestamp = System.currentTimeMillis()
        )
    }

    private fun parseSimplePulseOximeterData(data: ByteArray): PulseOximeterReading {
        Log.d(TAG, "=== SIMPLE PULSE OXIMETER PARSING ===")

        // Simple format: [SpO2, HR, PI] or similar
        val spO2 = (data[0].toInt() and 0xFF).toFloat()
        val heartRate = if (data.size > 1) data[1].toInt() and 0xFF else 0
        val perfusionIndex = if (data.size > 2) (data[2].toInt() and 0xFF).toFloat() / 10f else null

        Log.d(TAG, "Simple parsing: SpO2=$spO2%, HR=${heartRate}bpm, PI=$perfusionIndex")

        return PulseOximeterReading(
            spO2 = spO2,
            heartRate = heartRate,
            perfusionIndex = perfusionIndex,
            timestamp = System.currentTimeMillis()
        )
    }

    private fun parseAlternativePulseOximeterData(data: ByteArray): PulseOximeterReading {
        Log.d(TAG, "=== ALTERNATIVE PULSE OXIMETER PARSING ===")

        val candidates = mutableListOf<Triple<Float, Int, String>>()

        // Try different byte combinations for SpO2 and HR
        for (i in 0 until data.size - 1) {
            for (j in i + 1 until data.size) {
                val spO2Candidate = (data[i].toInt() and 0xFF).toFloat()
                val hrCandidate = data[j].toInt() and 0xFF

                if (spO2Candidate in 70f..100f && hrCandidate in 30..250) {
                    candidates.add(Triple(spO2Candidate, hrCandidate, "Bytes [$i,$j]: SpO2=$spO2Candidate%, HR=${hrCandidate}bpm"))
                }

                // Try 16-bit combinations
                if (j < data.size - 1) {
                    val spO2_16 = ((data[j].toInt() and 0xFF) shl 8 or (data[i].toInt() and 0xFF)).toFloat() / 100f
                    val hr_16 = (data[j + 1].toInt() and 0xFF) shl 8 or (data[j].toInt() and 0xFF)

                    if (spO2_16 in 70f..100f && hr_16 in 30..250) {
                        candidates.add(Triple(spO2_16, hr_16, "16-bit [$i,$j]: SpO2=$spO2_16%, HR=${hr_16}bpm"))
                    }
                }
            }
        }

        candidates.forEach { Log.d(TAG, "Candidate: ${it.third}") }

        val chosen = candidates.firstOrNull() ?: Triple(97f, 75, "Default fallback")
        Log.d(TAG, "=== CHOSEN: ${chosen.third} ===")

        return PulseOximeterReading(
            spO2 = chosen.first,
            heartRate = chosen.second,
            timestamp = System.currentTimeMillis()
        )
    }

    private fun parseTemperatureData(data: ByteArray): TemperatureReading {
        Log.d(TAG, "Parsing temperature data: ${data.joinToString(" ") { "%02x".format(it) }} (${data.size} bytes)")

        if (data.isEmpty()) {
            throw IllegalArgumentException("Empty data array")
        }

        // Check if this is your specific thermometer format (starts with 0xAA)
        return if (data.size == 5 && data[0].toInt() and 0xFF == 0xAA) {
            parseCustomThermometerData(data)
        } else {
            // Try different parsing methods based on data length and format
            when {
                data.size >= 5 -> parseStandardHealthThermometerData(data)
                data.size >= 4 -> parseSimpleFloatData(data)
                data.size >= 2 -> parseSimpleIntegerData(data)
                else -> throw IllegalArgumentException("Data too short: ${data.size} bytes")
            }
        }
    }

    private fun parseCustomThermometerData(data: ByteArray): TemperatureReading {
        Log.d(TAG, "=== CUSTOM THERMOMETER PARSING ===")
        Log.d(TAG, "Raw data: ${data.joinToString(" ") { "%02x".format(it) }}")
        Log.d(TAG, "Individual bytes: [0]=${data[0].toInt() and 0xFF}, [1]=${data[1].toInt() and 0xFF}, [2]=${data[2].toInt() and 0xFF}, [3]=${data[3].toInt() and 0xFF}, [4]=${data[4].toInt() and 0xFF}")

        val candidates = mutableListOf<Pair<Float, String>>()

        // Method 1: Last 2 bytes (little endian)
        val raw1 = (data[3].toInt() and 0xFF) or ((data[4].toInt() and 0xFF) shl 8)
        candidates.add(Pair(raw1 / 100.0f, "Last 2 bytes LE /100: $raw1"))
        candidates.add(Pair(raw1 / 10.0f, "Last 2 bytes LE /10: $raw1"))
        candidates.add(Pair(raw1.toFloat(), "Last 2 bytes LE raw: $raw1"))

        // Method 2: Last 2 bytes (big endian)
        val raw2 = (data[4].toInt() and 0xFF) or ((data[3].toInt() and 0xFF) shl 8)
        candidates.add(Pair(raw2 / 100.0f, "Last 2 bytes BE /100: $raw2"))
        candidates.add(Pair(raw2 / 10.0f, "Last 2 bytes BE /10: $raw2"))
        candidates.add(Pair(raw2.toFloat(), "Last 2 bytes BE raw: $raw2"))

        // Method 3: Middle 2 bytes (bytes 2-3, little endian)
        val raw3 = (data[2].toInt() and 0xFF) or ((data[3].toInt() and 0xFF) shl 8)
        candidates.add(Pair(raw3 / 100.0f, "Middle 2 bytes LE /100: $raw3"))
        candidates.add(Pair(raw3 / 10.0f, "Middle 2 bytes LE /10: $raw3"))
        candidates.add(Pair(raw3.toFloat(), "Middle 2 bytes LE raw: $raw3"))

        // Method 4: Middle 2 bytes (bytes 2-3, big endian)
        val raw4 = (data[3].toInt() and 0xFF) or ((data[2].toInt() and 0xFF) shl 8)
        candidates.add(Pair(raw4 / 100.0f, "Middle 2 bytes BE /100: $raw4"))
        candidates.add(Pair(raw4 / 10.0f, "Middle 2 bytes BE /10: $raw4"))
        candidates.add(Pair(raw4.toFloat(), "Middle 2 bytes BE raw: $raw4"))

        // Method 5: Single bytes
        for (i in 1..4) {
            val singleByte = data[i].toInt() and 0xFF
            candidates.add(Pair(singleByte / 10.0f, "Byte $i /10: $singleByte"))
            candidates.add(Pair(singleByte.toFloat(), "Byte $i raw: $singleByte"))
        }

        // Method 6: Try interpreting as signed values
        val signedRaw1 = data[3].toInt() or (data[4].toInt() shl 8)
        val signedRaw2 = data[4].toInt() or (data[3].toInt() shl 8)
        candidates.add(Pair(signedRaw1 / 100.0f, "Signed last 2 LE /100: $signedRaw1"))
        candidates.add(Pair(signedRaw1 / 10.0f, "Signed last 2 LE /10: $signedRaw1"))
        candidates.add(Pair(signedRaw2 / 100.0f, "Signed last 2 BE /100: $signedRaw2"))
        candidates.add(Pair(signedRaw2 / 10.0f, "Signed last 2 BE /10: $signedRaw2"))

        // Log all candidates
        Log.d(TAG, "=== ALL PARSING CANDIDATES ===")
        candidates.forEachIndexed { index, (temp, method) ->
            Log.d(TAG, "[$index] $method = $temp°C")
        }

        // Find reasonable temperature (20-50°C for medical thermometer)
        val reasonableCandidates = candidates.filter { it.first in 20.0f..50.0f }

        Log.d(TAG, "=== REASONABLE CANDIDATES (20-50°C) ===")
        reasonableCandidates.forEachIndexed { index, (temp, method) ->
            Log.d(TAG, "[$index] $method = $temp°C ✓")
        }

        val finalTemp = if (reasonableCandidates.isNotEmpty()) {
            val chosen = reasonableCandidates.first()
            Log.d(TAG, "=== CHOSEN: ${chosen.second} = ${chosen.first}°C ===")
            chosen.first
        } else {
            // If no reasonable candidates, log all and pick the first one for debugging
            Log.w(TAG, "No reasonable temperature found! Using first candidate for debugging.")
            val fallback = candidates.first()
            Log.d(TAG, "=== FALLBACK: ${fallback.second} = ${fallback.first}°C ===")
            fallback.first
        }

        return TemperatureReading(
            temperature = finalTemp,
            unit = "C",
            timestamp = System.currentTimeMillis()
        )
    }

    private fun parseStandardHealthThermometerData(data: ByteArray): TemperatureReading {
        Log.d(TAG, "Attempting standard Health Thermometer Profile parsing")

        val flags = data[0].toInt()
        val temperatureUnit = if ((flags and 0x01) != 0) "F" else "C"

        // Temperature value is in IEEE-11073 32-bit FLOAT format
        val temperatureValue = if (data.size >= 5) {
            val mantissa = ((data[3].toInt() and 0xFF) shl 16) or
                          ((data[2].toInt() and 0xFF) shl 8) or
                          (data[1].toInt() and 0xFF)
            val exponent = data[4].toInt()

            val temp = mantissa * Math.pow(10.0, exponent.toDouble()).toFloat()
            Log.d(TAG, "Standard parsing: mantissa=$mantissa, exponent=$exponent, temp=$temp")
            temp
        } else {
            0.0f
        }

        return TemperatureReading(
            temperature = temperatureValue,
            unit = temperatureUnit,
            timestamp = System.currentTimeMillis()
        )
    }

    private fun parseSimpleFloatData(data: ByteArray): TemperatureReading {
        Log.d(TAG, "Attempting simple float parsing")

        // Try parsing as a simple 32-bit float (little endian)
        val temperatureValue = java.nio.ByteBuffer.wrap(data).order(java.nio.ByteOrder.LITTLE_ENDIAN).float
        Log.d(TAG, "Simple float parsing: temp=$temperatureValue")

        return TemperatureReading(
            temperature = temperatureValue,
            unit = "C", // Default to Celsius
            timestamp = System.currentTimeMillis()
        )
    }

    private fun parseSimpleIntegerData(data: ByteArray): TemperatureReading {
        Log.d(TAG, "Attempting simple integer parsing")

        // Try parsing as a 16-bit integer (divide by 100 for decimal places)
        val rawValue = ((data[1].toInt() and 0xFF) shl 8) or (data[0].toInt() and 0xFF)
        val temperatureValue = rawValue / 100.0f
        Log.d(TAG, "Simple integer parsing: raw=$rawValue, temp=$temperatureValue")

        return TemperatureReading(
            temperature = temperatureValue,
            unit = "C", // Default to Celsius
            timestamp = System.currentTimeMillis()
        )
    }
    
    private fun hasBluetoothPermissions(): Boolean {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
        } else {
            ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
        }
    }
}

enum class ConnectionState {
    DISCONNECTED,
    CONNECTING,
    CONNECTED
}

data class TemperatureReading(
    val temperature: Float,
    val unit: String,
    val timestamp: Long
)

data class PulseOximeterReading(
    val spO2: Float, // Oxygen saturation percentage
    val heartRate: Int, // Heart rate in BPM
    val perfusionIndex: Float? = null, // PI value if available
    val signalQuality: Int? = null, // Signal quality indicator
    val timestamp: Long
)
