package com.example.medicalpoc

import android.Manifest
import android.app.AlertDialog
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.medicalpoc.bluetooth.*
import com.example.medicalpoc.data.*
import com.example.medicalpoc.models.*
import com.example.medicalpoc.ui.*
import android.util.Log
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class MainActivity : AppCompatActivity() {

    private lateinit var bluetoothDeviceScanner: BluetoothDeviceScanner
    private lateinit var bluetoothConnectionManager: BluetoothConnectionManager
    private lateinit var temperatureRepository: TemperatureRepository
    private lateinit var medicalReadingRepository: MedicalReadingRepository

    // Current selected device type
    private var selectedDeviceType: DeviceType = DeviceType.THERMOMETER
    
    // UI Components
    private lateinit var bluetoothStatusIcon: ImageView
    private lateinit var connectionStatusText: TextView
    private lateinit var deviceNameText: TextView
    private lateinit var scanButton: Button
    private lateinit var disconnectButton: Button
    private lateinit var currentTemperatureText: TextView
    private lateinit var lastReadingTimeText: TextView
    private lateinit var clearHistoryButton: Button
    private lateinit var temperatureHistoryRecyclerView: RecyclerView
    private lateinit var scanProgressBar: ProgressBar
    
    private lateinit var temperatureHistoryAdapter: TemperatureHistoryAdapter
    private var deviceSelectionDialog: AlertDialog? = null
    
    private val dateFormat = SimpleDateFormat("MMM dd 'at' h:mm a", Locale.getDefault())
    
    // Permission launcher
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            initializeBluetooth()
        } else {
            showPermissionDeniedMessage()
        }
    }
    
    // Bluetooth enable launcher
    private val enableBluetoothLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            updateBluetoothStatus()
        } else {
            Toast.makeText(this, "Bluetooth is required for this app", Toast.LENGTH_LONG).show()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // Get selected device type from intent
        val deviceTypeId = intent.getStringExtra(DeviceSelectionActivity.EXTRA_SELECTED_DEVICE_TYPE)
        Log.d("MainActivity", "Raw device type ID from intent: '$deviceTypeId'")

        // Enhanced device type resolution with debugging
        selectedDeviceType = when {
            deviceTypeId.isNullOrEmpty() -> {
                Log.w("MainActivity", "No device type ID provided, defaulting to THERMOMETER")
                DeviceType.THERMOMETER
            }
            deviceTypeId == "pulse_oximeter" -> {
                Log.d("MainActivity", "Pulse oximeter selected")
                DeviceType.PULSE_OXIMETER
            }
            else -> {
                val resolvedType = DeviceType.fromId(deviceTypeId)
                if (resolvedType != null) {
                    Log.d("MainActivity", "Resolved device type: ${resolvedType.id}")
                    resolvedType
                } else {
                    Log.w("MainActivity", "Unknown device type ID: '$deviceTypeId', defaulting to THERMOMETER")
                    DeviceType.THERMOMETER
                }
            }
        }

        Log.d("MainActivity", "Final selected device type: ${selectedDeviceType.id}")
        Log.d("MainActivity", "Device type display name resource ID: ${selectedDeviceType.displayName}")

        // Initialize database and repositories
        val database = TemperatureDatabase.getDatabase(this)
        temperatureRepository = TemperatureRepository(database.temperatureDao())
        medicalReadingRepository = MedicalReadingRepository(database.medicalReadingDao())

        initializeViews()
        setupRecyclerView()
        setupClickListeners()
        updateUIForDeviceType()

        // Check permissions and initialize
        checkPermissionsAndInitialize()
    }
    
    private fun initializeViews() {
        bluetoothStatusIcon = findViewById(R.id.bluetoothStatusIcon)
        connectionStatusText = findViewById(R.id.connectionStatusText)
        deviceNameText = findViewById(R.id.deviceNameText)
        scanButton = findViewById(R.id.scanButton)
        disconnectButton = findViewById(R.id.disconnectButton)
        currentTemperatureText = findViewById(R.id.currentTemperatureText)
        lastReadingTimeText = findViewById(R.id.lastReadingTimeText)
        clearHistoryButton = findViewById(R.id.clearHistoryButton)
        temperatureHistoryRecyclerView = findViewById(R.id.temperatureHistoryRecyclerView)
        scanProgressBar = findViewById(R.id.scanProgressBar)
    }
    
    private fun setupRecyclerView() {
        temperatureHistoryAdapter = TemperatureHistoryAdapter()
        temperatureHistoryRecyclerView.apply {
            adapter = temperatureHistoryAdapter
            layoutManager = LinearLayoutManager(this@MainActivity)
        }
        
        // Observe temperature history
        lifecycleScope.launch {
            temperatureRepository.getRecentReadings(50).collect { readings ->
                temperatureHistoryAdapter.submitList(readings)
                
                // Update current temperature display
                readings.firstOrNull()?.let { latestReading ->
                    currentTemperatureText.text = "${String.format("%.1f", latestReading.temperature)}°${latestReading.unit}"
                    lastReadingTimeText.text = "Last reading: ${dateFormat.format(Date(latestReading.timestamp))}"
                }
            }
        }
    }
    
    private fun setupClickListeners() {
        scanButton.setOnClickListener {
            startDeviceScanning()
        }
        
        disconnectButton.setOnClickListener {
            bluetoothConnectionManager.disconnect()
        }
        
        clearHistoryButton.setOnClickListener {
            showClearHistoryConfirmation()
        }
    }
    
    private fun checkPermissionsAndInitialize() {
        val requiredPermissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            arrayOf(
                Manifest.permission.BLUETOOTH_SCAN,
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        } else {
            arrayOf(
                Manifest.permission.BLUETOOTH,
                Manifest.permission.BLUETOOTH_ADMIN,
                Manifest.permission.ACCESS_FINE_LOCATION
            )
        }
        
        val missingPermissions = requiredPermissions.filter {
            ActivityCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
        }
        
        if (missingPermissions.isEmpty()) {
            initializeBluetooth()
        } else {
            requestPermissionLauncher.launch(missingPermissions.toTypedArray())
        }
    }
    
    private fun initializeBluetooth() {
        bluetoothDeviceScanner = BluetoothDeviceScanner(this)
        bluetoothConnectionManager = BluetoothConnectionManager(this)
        
        updateBluetoothStatus()
        observeBluetoothStates()
        
        // Check if Bluetooth is enabled
        val bluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager
        val bluetoothAdapter = bluetoothManager.adapter
        
        if (bluetoothAdapter?.isEnabled != true) {
            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            enableBluetoothLauncher.launch(enableBtIntent)
        }
    }
    
    private fun updateBluetoothStatus() {
        val isEnabled = bluetoothDeviceScanner.isBluetoothEnabled()
        bluetoothStatusIcon.setImageResource(
            if (isEnabled) R.drawable.ic_bluetooth else R.drawable.ic_bluetooth_disabled
        )
        scanButton.isEnabled = isEnabled
    }
    
    private fun observeBluetoothStates() {
        // Observe connection state
        lifecycleScope.launch {
            bluetoothConnectionManager.connectionState.collect { state ->
                updateConnectionUI(state)
            }
        }
        
        // Observe temperature data
        lifecycleScope.launch {
            bluetoothConnectionManager.temperatureData.collect { reading ->
                reading?.let {
                    saveTemperatureReading(it)
                }
            }
        }

        // Observe pulse oximeter data
        lifecycleScope.launch {
            bluetoothConnectionManager.pulseOximeterData.collect { reading ->
                reading?.let {
                    savePulseOximeterReading(it)
                }
            }
        }
        
        // Observe connection errors
        lifecycleScope.launch {
            bluetoothConnectionManager.connectionError.collect { error ->
                error?.let {
                    Toast.makeText(this@MainActivity, "Connection error: $it", Toast.LENGTH_LONG).show()
                }
            }
        }
    }
    
    private fun updateConnectionUI(state: ConnectionState) {
        when (state) {
            ConnectionState.DISCONNECTED -> {
                connectionStatusText.text = "Disconnected"
                connectionStatusText.setTextColor(getColor(android.R.color.holo_red_dark))
                deviceNameText.text = "No device connected"
                disconnectButton.isEnabled = false
                scanButton.isEnabled = true
            }
            ConnectionState.CONNECTING -> {
                connectionStatusText.text = "Connecting..."
                connectionStatusText.setTextColor(getColor(android.R.color.holo_orange_dark))
                disconnectButton.isEnabled = false
                scanButton.isEnabled = false
            }
            ConnectionState.CONNECTED -> {
                connectionStatusText.text = "Connected"
                connectionStatusText.setTextColor(getColor(android.R.color.holo_green_dark))
                disconnectButton.isEnabled = true
                scanButton.isEnabled = false
            }
        }
    }
    
    private fun saveTemperatureReading(reading: TemperatureReading) {
        lifecycleScope.launch {
            temperatureRepository.saveReading(reading)
        }
    }

    private fun savePulseOximeterReading(reading: PulseOximeterReading) {
        lifecycleScope.launch {
            val measurement = PulseOximeterMeasurement(
                deviceType = DeviceType.PULSE_OXIMETER,
                timestamp = reading.timestamp,
                deviceName = deviceNameText.text.toString(),
                primaryValue = reading.spO2,
                unit = "%",
                heartRate = reading.heartRate,
                perfusionIndex = reading.perfusionIndex
            )
            medicalReadingRepository.insertMeasurement(measurement)

            // Update UI
            runOnUiThread {
                updatePulseOximeterDisplay(reading)
            }
        }
    }
    
    private fun startDeviceScanning() {
        showDeviceSelectionDialog()
    }
    
    private fun showPermissionDeniedMessage() {
        AlertDialog.Builder(this)
            .setTitle("Permissions Required")
            .setMessage("This app requires Bluetooth and location permissions to scan for and connect to thermometer devices.")
            .setPositiveButton("OK") { _, _ -> }
            .show()
    }
    
    private fun showClearHistoryConfirmation() {
        AlertDialog.Builder(this)
            .setTitle("Clear History")
            .setMessage("Are you sure you want to delete all temperature readings?")
            .setPositiveButton("Clear") { _, _ ->
                lifecycleScope.launch {
                    temperatureRepository.deleteAllReadings()
                    Toast.makeText(this@MainActivity, "History cleared", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("Cancel", null)
            .show()
    }

    private fun showDeviceSelectionDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_device_selection, null)
        val devicesRecyclerView = dialogView.findViewById<RecyclerView>(R.id.devicesRecyclerView)
        val scanStatusText = dialogView.findViewById<TextView>(R.id.scanStatusText)
        val scanProgressBar = dialogView.findViewById<ProgressBar>(R.id.scanProgressBar)
        val cancelButton = dialogView.findViewById<Button>(R.id.cancelButton)
        val rescanButton = dialogView.findViewById<Button>(R.id.rescanButton)

        val deviceAdapter = BluetoothDeviceAdapter { scanResult ->
            deviceSelectionDialog?.dismiss()
            bluetoothDeviceScanner.stopScan()

            deviceNameText.text = scanResult.device.name ?: "Unknown Device"
            bluetoothConnectionManager.setDeviceType(selectedDeviceType)
            bluetoothConnectionManager.connectToDevice(scanResult.device)
        }

        devicesRecyclerView.apply {
            adapter = deviceAdapter
            layoutManager = LinearLayoutManager(this@MainActivity)
        }

        deviceSelectionDialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(false)
            .create()

        // Observe scan results
        lifecycleScope.launch {
            bluetoothDeviceScanner.scanResults.collect { results ->
                deviceAdapter.submitList(results)
                scanStatusText.text = if (results.isEmpty()) {
                    "Scanning for devices..."
                } else {
                    "${results.size} device(s) found"
                }
            }
        }

        // Observe scan state
        lifecycleScope.launch {
            bluetoothDeviceScanner.isScanning.collect { isScanning ->
                scanProgressBar.visibility = if (isScanning) View.VISIBLE else View.GONE
                rescanButton.isEnabled = !isScanning

                if (!isScanning) {
                    scanStatusText.text = "Scan completed"
                }
            }
        }

        // Observe scan errors
        lifecycleScope.launch {
            bluetoothDeviceScanner.scanError.collect { error ->
                error?.let {
                    scanStatusText.text = "Error: $it"
                    Toast.makeText(this@MainActivity, "Scan error: $it", Toast.LENGTH_LONG).show()
                }
            }
        }

        cancelButton.setOnClickListener {
            bluetoothDeviceScanner.stopScan()
            deviceSelectionDialog?.dismiss()
        }

        rescanButton.setOnClickListener {
            bluetoothDeviceScanner.startScan(selectedDeviceType)
        }

        deviceSelectionDialog?.show()
        Log.d("MainActivity", "Starting scan for device type: ${selectedDeviceType.id}")
        bluetoothDeviceScanner.startScan(selectedDeviceType)
    }

    private fun updatePulseOximeterDisplay(reading: PulseOximeterReading) {
        // Update primary display with SpO2
        currentTemperatureText.text = "${reading.spO2.toInt()}%"

        // Update last reading time
        val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
        lastReadingTimeText.text = "Last reading: ${timeFormat.format(Date(reading.timestamp))}"

        // Show additional info in a toast or secondary display
        val additionalInfo = "SpO2: ${reading.spO2.toInt()}% | HR: ${reading.heartRate} BPM" +
                if (reading.perfusionIndex != null) " | PI: ${String.format("%.1f", reading.perfusionIndex)}" else ""

        Toast.makeText(this, additionalInfo, Toast.LENGTH_SHORT).show()

        Log.d("MainActivity", "Pulse oximeter display updated: $additionalInfo")
    }

    private fun updateUIForDeviceType() {
        Log.d("MainActivity", "updateUIForDeviceType called with: ${selectedDeviceType.id}")

        try {
            // Update title and UI elements based on selected device type
            val title = getString(selectedDeviceType.displayName)
            supportActionBar?.title = title
            Log.d("MainActivity", "Set title to: $title")

            // Update temperature text to show appropriate measurement
            val measurementText = when (selectedDeviceType) {
                DeviceType.THERMOMETER -> {
                    Log.d("MainActivity", "Setting UI for THERMOMETER")
                    "-- °C"
                }
                DeviceType.PULSE_OXIMETER -> {
                    Log.d("MainActivity", "Setting UI for PULSE_OXIMETER")
                    "-- %"
                }
                DeviceType.BLOOD_PRESSURE_MONITOR -> {
                    Log.d("MainActivity", "Setting UI for BLOOD_PRESSURE_MONITOR")
                    "-- / -- mmHg"
                }
                DeviceType.HBCHECK_BIOSENSE -> {
                    Log.d("MainActivity", "Setting UI for HBCHECK_BIOSENSE")
                    "-- g/dL"
                }
                DeviceType.A1_CHECK_ANALYZER -> {
                    Log.d("MainActivity", "Setting UI for A1_CHECK_ANALYZER")
                    "-- %"
                }
                DeviceType.BLOOD_LIPID_METER -> {
                    Log.d("MainActivity", "Setting UI for BLOOD_LIPID_METER")
                    "-- mg/dL"
                }
                DeviceType.DIGITAL_STETHOSCOPE -> {
                    Log.d("MainActivity", "Setting UI for DIGITAL_STETHOSCOPE")
                    "-- BPM"
                }
                DeviceType.SPIROMETER -> {
                    Log.d("MainActivity", "Setting UI for SPIROMETER")
                    "-- L"
                }
            }

            currentTemperatureText.text = measurementText
            Log.d("MainActivity", "UI updated successfully: $measurementText")

        } catch (e: Exception) {
            Log.e("MainActivity", "Error updating UI for device type: ${selectedDeviceType.id}", e)
            // Fallback to default
            currentTemperatureText.text = "-- °C"
            supportActionBar?.title = "Medical Device"
        }
    }
}
