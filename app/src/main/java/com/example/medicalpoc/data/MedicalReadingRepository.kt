package com.example.medicalpoc.data

import kotlinx.coroutines.flow.Flow
import com.example.medicalpoc.models.*

class MedicalReadingRepository(
    private val medicalReadingDao: MedicalReadingDao
) {
    
    fun getAllReadings(): Flow<List<MedicalReadingEntity>> = 
        medicalReadingDao.getAllReadings()
    
    fun getReadingsByDeviceType(deviceType: DeviceType): Flow<List<MedicalReadingEntity>> = 
        medicalReadingDao.getReadingsByDeviceType(deviceType.id)
    
    fun getRecentReadings(limit: Int): Flow<List<MedicalReadingEntity>> = 
        medicalReadingDao.getRecentReadings(limit)
    
    fun getRecentReadingsByDeviceType(deviceType: DeviceType, limit: Int): Flow<List<MedicalReadingEntity>> = 
        medicalReadingDao.getRecentReadingsByDeviceType(deviceType.id, limit)
    
    suspend fun insertReading(reading: MedicalReadingEntity) {
        medicalReadingDao.insertReading(reading)
    }
    
    suspend fun insertMeasurement(measurement: MedicalMeasurement) {
        val entity = when (measurement) {
            is TemperatureMeasurement -> MedicalReadingEntity(
                deviceType = measurement.deviceType.id,
                primaryValue = measurement.primaryValue,
                unit = measurement.unit,
                timestamp = measurement.timestamp,
                deviceName = measurement.deviceName,
                additionalData = measurement.bodyLocation
            )
            
            is PulseOximeterMeasurement -> MedicalReadingEntity(
                deviceType = measurement.deviceType.id,
                primaryValue = measurement.primaryValue,
                unit = measurement.unit,
                timestamp = measurement.timestamp,
                deviceName = measurement.deviceName,
                secondaryValue = measurement.heartRate.toFloat(),
                tertiaryValue = measurement.perfusionIndex,
                additionalData = "SpO2: ${measurement.primaryValue}%, HR: ${measurement.heartRate} BPM"
            )
            
            is HemoglobinMeasurement -> MedicalReadingEntity(
                deviceType = measurement.deviceType.id,
                primaryValue = measurement.primaryValue,
                unit = measurement.unit,
                timestamp = measurement.timestamp,
                deviceName = measurement.deviceName,
                secondaryValue = measurement.hematocrit,
                additionalData = "Hb: ${measurement.primaryValue} g/dL"
            )
            
            is HbA1cMeasurement -> MedicalReadingEntity(
                deviceType = measurement.deviceType.id,
                primaryValue = measurement.primaryValue,
                unit = measurement.unit,
                timestamp = measurement.timestamp,
                deviceName = measurement.deviceName,
                secondaryValue = measurement.estimatedAverageGlucose,
                additionalData = "HbA1c: ${measurement.primaryValue}%"
            )
            
            is BloodLipidMeasurement -> MedicalReadingEntity(
                deviceType = measurement.deviceType.id,
                primaryValue = measurement.primaryValue,
                unit = measurement.unit,
                timestamp = measurement.timestamp,
                deviceName = measurement.deviceName,
                secondaryValue = measurement.hdlCholesterol,
                tertiaryValue = measurement.ldlCholesterol,
                additionalData = "TC: ${measurement.primaryValue}, HDL: ${measurement.hdlCholesterol}, LDL: ${measurement.ldlCholesterol}, TG: ${measurement.triglycerides}"
            )
            
            is StethoscopeMeasurement -> MedicalReadingEntity(
                deviceType = measurement.deviceType.id,
                primaryValue = measurement.primaryValue,
                unit = measurement.unit,
                timestamp = measurement.timestamp,
                deviceName = measurement.deviceName,
                additionalData = "HR: ${measurement.primaryValue} BPM, Rhythm: ${measurement.rhythm ?: "Normal"}"
            )
            
            is BloodPressureMeasurement -> MedicalReadingEntity(
                deviceType = measurement.deviceType.id,
                primaryValue = measurement.primaryValue,
                unit = measurement.unit,
                timestamp = measurement.timestamp,
                deviceName = measurement.deviceName,
                secondaryValue = measurement.diastolicPressure,
                tertiaryValue = measurement.meanArterialPressure,
                additionalData = "${measurement.primaryValue.toInt()}/${measurement.diastolicPressure.toInt()} mmHg${measurement.heartRate?.let { ", HR: $it BPM" } ?: ""}"
            )
            
            is SpirometerMeasurement -> MedicalReadingEntity(
                deviceType = measurement.deviceType.id,
                primaryValue = measurement.primaryValue,
                unit = measurement.unit,
                timestamp = measurement.timestamp,
                deviceName = measurement.deviceName,
                secondaryValue = measurement.fvc,
                tertiaryValue = measurement.fev1FvcRatio,
                additionalData = "FEV1: ${measurement.primaryValue}L, FVC: ${measurement.fvc}L, Ratio: ${measurement.fev1FvcRatio}"
            )
        }
        
        medicalReadingDao.insertReading(entity)
    }
    
    suspend fun deleteReading(reading: MedicalReadingEntity) {
        medicalReadingDao.deleteReading(reading)
    }
    
    suspend fun deleteReadingsByDeviceType(deviceType: DeviceType) {
        medicalReadingDao.deleteReadingsByDeviceType(deviceType.id)
    }
    
    suspend fun deleteAllReadings() {
        medicalReadingDao.deleteAllReadings()
    }
    
    suspend fun getReadingCount(): Int = medicalReadingDao.getReadingCount()
    
    suspend fun getReadingCountByDeviceType(deviceType: DeviceType): Int = 
        medicalReadingDao.getReadingCountByDeviceType(deviceType.id)
}
