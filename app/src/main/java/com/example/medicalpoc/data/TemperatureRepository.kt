package com.example.medicalpoc.data

import com.example.medicalpoc.bluetooth.TemperatureReading
import kotlinx.coroutines.flow.Flow
import java.util.concurrent.TimeUnit

class TemperatureRepository(private val temperatureDao: TemperatureDao) {
    
    fun getAllReadings(): Flow<List<TemperatureEntity>> {
        return temperatureDao.getAllReadings()
    }
    
    fun getRecentReadings(limit: Int = 50): Flow<List<TemperatureEntity>> {
        return temperatureDao.getRecentReadings(limit)
    }
    
    fun getReadingsByDateRange(startTime: Long, endTime: Long): Flow<List<TemperatureEntity>> {
        return temperatureDao.getReadingsByDateRange(startTime, endTime)
    }
    
    suspend fun saveReading(
        reading: TemperatureReading, 
        deviceAddress: String? = null, 
        deviceName: String? = null
    ) {
        val entity = TemperatureEntity(
            temperature = reading.temperature,
            unit = reading.unit,
            timestamp = reading.timestamp,
            deviceAddress = deviceAddress,
            deviceName = deviceName
        )
        temperatureDao.insertReading(entity)
    }
    
    suspend fun deleteReading(reading: TemperatureEntity) {
        temperatureDao.deleteReading(reading)
    }
    
    suspend fun deleteOldReadings(daysToKeep: Int = 30) {
        val cutoffTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(daysToKeep.toLong())
        temperatureDao.deleteOldReadings(cutoffTime)
    }
    
    suspend fun deleteAllReadings() {
        temperatureDao.deleteAllReadings()
    }
    
    suspend fun getReadingCount(): Int {
        return temperatureDao.getReadingCount()
    }
}
