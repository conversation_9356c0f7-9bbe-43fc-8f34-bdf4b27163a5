package com.example.medicalpoc.data

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "medical_readings")
data class MedicalReadingEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val deviceType: String, // DeviceType.id
    val primaryValue: Float, // Main measurement value
    val unit: String,
    val timestamp: Long,
    val deviceName: String? = null,

    // Additional values stored as JSON or separate fields
    val secondaryValue: Float? = null, // e.g., heart rate for pulse oximeter
    val tertiaryValue: Float? = null,  // e.g., diastolic for blood pressure
    val additionalData: String? = null, // JSON for complex data
    val notes: String? = null
)

// Keep the old entity for backward compatibility during migration
@Entity(tableName = "temperature_readings")
data class TemperatureEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val temperature: Float,
    val unit: String,
    val timestamp: Long,
    val deviceAddress: String? = null,
    val deviceName: String? = null
)
