package com.example.medicalpoc.data

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context

@Database(
    entities = [TemperatureEntity::class, MedicalReadingEntity::class],
    version = 2,
    exportSchema = false
)
abstract class TemperatureDatabase : RoomDatabase() {

    abstract fun temperatureDao(): TemperatureDao
    abstract fun medicalReadingDao(): MedicalReadingDao
    
    companion object {
        @Volatile
        private var INSTANCE: TemperatureDatabase? = null

        // Migration from version 1 to 2
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Create the new medical_readings table
                database.execSQL("""
                    CREATE TABLE IF NOT EXISTS `medical_readings` (
                        `id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL,
                        `deviceType` TEXT NOT NULL,
                        `primaryValue` REAL NOT NULL,
                        `unit` TEXT NOT NULL,
                        `timestamp` INTEGER NOT NULL,
                        `deviceName` TEXT,
                        `secondaryValue` REAL,
                        `tertiaryValue` REAL,
                        `additionalData` TEXT,
                        `notes` TEXT
                    )
                """.trimIndent())
            }
        }

        fun getDatabase(context: Context): TemperatureDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    TemperatureDatabase::class.java,
                    "temperature_database"
                )
                .fallbackToDestructiveMigration() // Allow destructive migration - will recreate DB
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
}
