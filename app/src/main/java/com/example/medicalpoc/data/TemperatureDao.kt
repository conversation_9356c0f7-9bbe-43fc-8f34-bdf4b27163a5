package com.example.medicalpoc.data

import androidx.room.*
import kotlinx.coroutines.flow.Flow

@Dao
interface MedicalReadingDao {

    @Query("SELECT * FROM medical_readings ORDER BY timestamp DESC")
    fun getAllReadings(): Flow<List<MedicalReadingEntity>>

    @Query("SELECT * FROM medical_readings WHERE deviceType = :deviceType ORDER BY timestamp DESC")
    fun getReadingsByDeviceType(deviceType: String): Flow<List<MedicalReadingEntity>>

    @Query("SELECT * FROM medical_readings ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentReadings(limit: Int): Flow<List<MedicalReadingEntity>>

    @Query("SELECT * FROM medical_readings WHERE deviceType = :deviceType ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentReadingsByDeviceType(deviceType: String, limit: Int): Flow<List<MedicalReadingEntity>>

    @Insert
    suspend fun insertReading(reading: MedicalReadingEntity)

    @Insert
    suspend fun insertReadings(readings: List<MedicalReadingEntity>)

    @Delete
    suspend fun deleteReading(reading: MedicalReadingEntity)

    @Query("DELETE FROM medical_readings WHERE deviceType = :deviceType")
    suspend fun deleteReadingsByDeviceType(deviceType: String)

    @Query("DELETE FROM medical_readings WHERE timestamp < :cutoffTime")
    suspend fun deleteOldReadings(cutoffTime: Long)

    @Query("DELETE FROM medical_readings")
    suspend fun deleteAllReadings()

    @Query("SELECT COUNT(*) FROM medical_readings")
    suspend fun getReadingCount(): Int

    @Query("SELECT COUNT(*) FROM medical_readings WHERE deviceType = :deviceType")
    suspend fun getReadingCountByDeviceType(deviceType: String): Int
}

@Dao
interface TemperatureDao {
    
    @Query("SELECT * FROM temperature_readings ORDER BY timestamp DESC")
    fun getAllReadings(): Flow<List<TemperatureEntity>>
    
    @Query("SELECT * FROM temperature_readings ORDER BY timestamp DESC LIMIT :limit")
    fun getRecentReadings(limit: Int): Flow<List<TemperatureEntity>>
    
    @Query("SELECT * FROM temperature_readings WHERE timestamp >= :startTime AND timestamp <= :endTime ORDER BY timestamp DESC")
    fun getReadingsByDateRange(startTime: Long, endTime: Long): Flow<List<TemperatureEntity>>
    
    @Insert
    suspend fun insertReading(reading: TemperatureEntity)
    
    @Insert
    suspend fun insertReadings(readings: List<TemperatureEntity>)
    
    @Delete
    suspend fun deleteReading(reading: TemperatureEntity)
    
    @Query("DELETE FROM temperature_readings WHERE timestamp < :cutoffTime")
    suspend fun deleteOldReadings(cutoffTime: Long)
    
    @Query("DELETE FROM temperature_readings")
    suspend fun deleteAllReadings()
    
    @Query("SELECT COUNT(*) FROM temperature_readings")
    suspend fun getReadingCount(): Int
}
