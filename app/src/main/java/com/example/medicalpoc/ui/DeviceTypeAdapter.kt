package com.example.medicalpoc.ui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import com.example.medicalpoc.R
import com.example.medicalpoc.models.DeviceType

class DeviceTypeAdapter(
    private val deviceTypes: List<DeviceType>,
    private val onDeviceTypeClick: (DeviceType) -> Unit
) : RecyclerView.Adapter<DeviceTypeAdapter.DeviceTypeViewHolder>() {
    
    class DeviceTypeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val cardView: CardView = itemView.findViewById(R.id.deviceTypeCard)
        val iconImageView: ImageView = itemView.findViewById(R.id.deviceTypeIcon)
        val nameTextView: TextView = itemView.findViewById(R.id.deviceTypeName)
        val descriptionTextView: TextView = itemView.findViewById(R.id.deviceTypeDescription)
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceTypeViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_device_type, parent, false)
        return DeviceTypeViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: DeviceTypeViewHolder, position: Int) {
        val deviceType = deviceTypes[position]
        val context = holder.itemView.context
        
        // Set device icon
        holder.iconImageView.setImageResource(deviceType.icon)
        
        // Set device name
        holder.nameTextView.text = context.getString(deviceType.displayName)
        
        // Set device description
        holder.descriptionTextView.text = context.getString(deviceType.description)
        
        // Set click listener
        holder.cardView.setOnClickListener {
            onDeviceTypeClick(deviceType)
        }
        
        // Add ripple effect and elevation
        holder.cardView.isClickable = true
        holder.cardView.isFocusable = true
        holder.cardView.cardElevation = 4f
        
        // Add hover effect
        holder.cardView.setOnTouchListener { view, event ->
            when (event.action) {
                android.view.MotionEvent.ACTION_DOWN -> {
                    holder.cardView.cardElevation = 8f
                }
                android.view.MotionEvent.ACTION_UP, 
                android.view.MotionEvent.ACTION_CANCEL -> {
                    holder.cardView.cardElevation = 4f
                }
            }
            false
        }
    }
    
    override fun getItemCount(): Int = deviceTypes.size
}
