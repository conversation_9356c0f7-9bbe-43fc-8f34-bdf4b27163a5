package com.example.medicalpoc.ui

import android.bluetooth.le.ScanResult
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.medicalpoc.R

class BluetoothDeviceAdapter(
    private val onDeviceClick: (ScanResult) -> Unit
) : ListAdapter<ScanResult, BluetoothDeviceAdapter.ViewHolder>(DiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_bluetooth_device, parent, false)
        return ViewHolder(view, onDeviceClick)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    class ViewHolder(
        itemView: View,
        private val onDeviceClick: (ScanResult) -> Unit
    ) : RecyclerView.ViewHolder(itemView) {
        
        private val deviceNameText: TextView = itemView.findViewById(R.id.deviceNameText)
        private val deviceAddressText: TextView = itemView.findViewById(R.id.deviceAddressText)
        private val rssiText: TextView = itemView.findViewById(R.id.rssiText)
        
        fun bind(scanResult: ScanResult) {
            val device = scanResult.device
            
            deviceNameText.text = device.name ?: "Unknown Device"
            deviceAddressText.text = device.address
            rssiText.text = "${scanResult.rssi} dBm"
            
            itemView.setOnClickListener {
                onDeviceClick(scanResult)
            }
        }
    }
    
    class DiffCallback : DiffUtil.ItemCallback<ScanResult>() {
        override fun areItemsTheSame(oldItem: ScanResult, newItem: ScanResult): Boolean {
            return oldItem.device.address == newItem.device.address
        }
        
        override fun areContentsTheSame(oldItem: ScanResult, newItem: ScanResult): Boolean {
            return oldItem.device.address == newItem.device.address && 
                   oldItem.rssi == newItem.rssi
        }
    }
}
