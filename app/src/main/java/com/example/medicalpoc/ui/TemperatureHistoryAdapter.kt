package com.example.medicalpoc.ui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.medicalpoc.R
import com.example.medicalpoc.data.TemperatureEntity
import java.text.SimpleDateFormat
import java.util.*

class TemperatureHistoryAdapter : ListAdapter<TemperatureEntity, TemperatureHistoryAdapter.ViewHolder>(DiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_temperature_reading, parent, false)
        return ViewHolder(view)
    }
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val temperatureText: TextView = itemView.findViewById(R.id.temperatureText)
        private val timestampText: TextView = itemView.findViewById(R.id.timestampText)
        private val deviceText: TextView = itemView.findViewById(R.id.deviceText)
        
        private val dateFormat = SimpleDateFormat("MMM dd, yyyy 'at' h:mm a", Locale.getDefault())
        
        fun bind(reading: TemperatureEntity) {
            temperatureText.text = "${String.format("%.1f", reading.temperature)}°${reading.unit}"
            timestampText.text = dateFormat.format(Date(reading.timestamp))
            
            if (!reading.deviceName.isNullOrEmpty()) {
                deviceText.text = reading.deviceName
                deviceText.visibility = View.VISIBLE
            } else {
                deviceText.visibility = View.GONE
            }
        }
    }
    
    class DiffCallback : DiffUtil.ItemCallback<TemperatureEntity>() {
        override fun areItemsTheSame(oldItem: TemperatureEntity, newItem: TemperatureEntity): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: TemperatureEntity, newItem: TemperatureEntity): Boolean {
            return oldItem == newItem
        }
    }
}
