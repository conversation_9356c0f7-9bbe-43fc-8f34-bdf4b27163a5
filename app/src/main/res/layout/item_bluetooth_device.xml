<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:gravity="center_vertical"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <ImageView
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:src="@drawable/ic_bluetooth"
        android:contentDescription="Bluetooth Device"
        android:layout_marginEnd="16dp" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/deviceNameText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Device Name"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/deviceAddressText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="00:00:00:00:00:00"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginTop="2dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/rssiText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="-50 dBm"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginStart="8dp" />

</LinearLayout>
