<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light"
    android:padding="16dp">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="24dp"
        android:gravity="center">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:src="@drawable/ic_medical_hub"
            android:layout_marginBottom="16dp"
            android:contentDescription="Medical Device Hub Icon" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/select_device_type"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/device_selection_subtitle"
            android:textSize="16sp"
            android:textColor="@color/secondary_text"
            android:gravity="center"
            android:layout_marginHorizontal="16dp" />

    </LinearLayout>

    <!-- Device Types Grid -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/deviceTypeRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clipToPadding="false"
        android:padding="8dp" />

    <!-- Footer -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Select a device type to begin scanning for compatible devices"
        android:textSize="14sp"
        android:textColor="@color/secondary_text"
        android:gravity="center"
        android:layout_marginTop="16dp"
        android:padding="8dp" />

</LinearLayout>
