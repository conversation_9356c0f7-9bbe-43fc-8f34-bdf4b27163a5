<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/deviceTypeCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@android:color/white"
    android:foreground="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:gravity="center">

        <!-- Device Icon -->
        <ImageView
            android:id="@+id/deviceTypeIcon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_marginBottom="12dp"
            android:scaleType="centerInside"
            android:src="@drawable/ic_thermometer"
            android:contentDescription="Device Icon" />

        <!-- Device Name -->
        <TextView
            android:id="@+id/deviceTypeName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Thermometer"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/primary_text"
            android:gravity="center"
            android:layout_marginBottom="8dp"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- Device Description -->
        <TextView
            android:id="@+id/deviceTypeDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Measure body temperature"
            android:textSize="12sp"
            android:textColor="@color/secondary_text"
            android:gravity="center"
            android:maxLines="3"
            android:ellipsize="end"
            android:lineSpacingMultiplier="1.2" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
