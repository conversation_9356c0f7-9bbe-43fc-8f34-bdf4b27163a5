<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Select Thermometer Device"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/scanStatusText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Scanning for devices..."
        android:textSize="14sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginBottom="8dp" />

    <ProgressBar
        android:id="@+id/scanProgressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/devicesRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:maxHeight="300dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/cancelButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Cancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/rescanButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Rescan" />

    </LinearLayout>

</LinearLayout>
