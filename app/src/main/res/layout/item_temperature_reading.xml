<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:background="@drawable/card_background"
    android:orientation="horizontal"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/temperatureText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="98.6°F"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/purple_700" />

            <TextView
                android:id="@+id/timestampText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Today, 2:30 PM"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/deviceText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Thermometer Device"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="2dp"
                android:visibility="gone" />

        </LinearLayout>

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_thermometer"
            android:contentDescription="Temperature Reading"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>
