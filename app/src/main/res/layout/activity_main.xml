<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res/auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Medical Thermometer"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/purple_700" />

        <ImageView
            android:id="@+id/bluetoothStatusIcon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_bluetooth_disabled"
            android:contentDescription="Bluetooth Status" />

    </LinearLayout>

    <!-- Connection Status Card -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="@drawable/card_background"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Device Connection"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/connectionStatusText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Disconnected"
                android:textSize="16sp"
                android:textColor="@color/design_default_color_error"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/deviceNameText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No device connected"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="12dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/scanButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Scan for Devices"
                    android:layout_marginEnd="8dp" />

                <Button
                    android:id="@+id/disconnectButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Disconnect"
                    android:enabled="false"
                    android:layout_marginStart="8dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- Current Temperature Card -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:background="@drawable/card_background"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Current Temperature"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <TextView
                android:id="@+id/currentTemperatureText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="--.-°"
                android:textSize="48sp"
                android:textStyle="bold"
                android:textColor="@color/purple_700"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/lastReadingTimeText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No readings yet"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray" />

        </LinearLayout>

    </LinearLayout>

    <!-- Temperature History Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Temperature History"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/clearHistoryButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Clear"
            style="@style/Widget.Material3.Button.TextButton" />

    </LinearLayout>

    <!-- Temperature History RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/temperatureHistoryRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        tools:listitem="@layout/item_temperature_reading" />

    <!-- Progress Bar for Scanning -->
    <ProgressBar
        android:id="@+id/scanProgressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</LinearLayout>
