# Quick Setup Guide

## 🚀 Getting Started in 5 Minutes

### Prerequisites
- Android Studio (latest version recommended)
- Android device with Bluetooth LE support
- Medical thermometer with Bluetooth capability

### 1. Project Setup

```bash
# Clone the repository
git clone <your-repo-url>
cd medicalpoc

# Open in Android Studio
# File -> Open -> Select project folder
```

### 2. Build Configuration

The project uses **Gradle with Kotlin DSL**. Key configuration files:

**`app/build.gradle.kts`**
```kotlin
android {
    compileSdk = 34
    defaultConfig {
        minSdk = 21
        targetSdk = 34
    }
}

dependencies {
    // Bluetooth & Coroutines
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3")
    
    // Room Database
    implementation("androidx.room:room-runtime:2.6.0")
    implementation("androidx.room:room-ktx:2.6.0")
    kapt("androidx.room:room-compiler:2.6.0")
    
    // UI Components
    implementation("androidx.recyclerview:recyclerview:1.3.2")
    implementation("com.google.android.material:material:1.10.0")
}
```

### 3. Permissions Setup

**`app/src/main/AndroidManifest.xml`**
```xml
<!-- Bluetooth permissions for Android 12+ -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" 
    android:usesPermissionFlags="neverForLocation" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

<!-- Location permission for BLE scanning (required on older Android versions) -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

<!-- Legacy Bluetooth permissions -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

<!-- Declare BLE hardware requirement -->
<uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
```

### 4. Build & Install

```bash
# Set Java version (required for Android Gradle Plugin 8.x)
export JAVA_HOME=/path/to/jdk-17

# Build debug APK
./gradlew assembleDebug

# Install on connected device
adb install app/build/outputs/apk/debug/app-debug.apk

# Or use Android Studio
# Run -> Run 'app'
```

## 📱 First Run

### 1. Launch App
- Open "Medical Thermometer" app on your device
- Grant requested permissions when prompted

### 2. Connect Thermometer
1. Put your thermometer in **pairing/discoverable mode**
2. Tap **"Scan for Devices"** in the app
3. Select your thermometer from the list
4. Wait for connection confirmation (green status)

### 3. Test Temperature Reading
1. Use your thermometer normally (forehead/ear measurement)
2. Temperature should appear automatically in the app
3. Check the history section for saved readings

## 🔧 Troubleshooting

### Common Issues & Solutions

**❌ "No devices found"**
```
✅ Solutions:
- Enable Bluetooth on your phone
- Grant Location permission (required for BLE scanning)
- Put thermometer in pairing mode
- Try scanning again
```

**❌ "Connection failed"**
```
✅ Solutions:
- Restart Bluetooth: Settings > Bluetooth > Turn off/on
- Clear Bluetooth cache: Settings > Apps > Bluetooth > Storage > Clear Cache
- Ensure thermometer isn't connected to other devices
- Move closer to the thermometer
```

**❌ "Permission denied"**
```
✅ Solutions:
- Go to Settings > Apps > Medical Thermometer > Permissions
- Enable all Bluetooth and Location permissions
- Restart the app
```

**❌ "Infinity temperature readings"**
```
✅ This indicates custom protocol parsing issues:
- Check logcat for detailed parsing attempts
- The app tries 20+ parsing methods automatically
- Contact support with your thermometer model info
```

### Debug Logging

Enable detailed logging to diagnose issues:

```bash
# Filter for app-specific logs
adb logcat | grep -E "(BluetoothConnectionManager|medicalpoc)"

# Or view all parsing attempts
adb logcat | grep -E "(CUSTOM THERMOMETER|PARSING CANDIDATES)"
```

## 🎯 Supported Devices

### Tested Thermometers
- ✅ Standard Health Thermometer Profile devices
- ✅ Custom protocol devices (5-byte format with 0xAA header)
- ✅ Most BLE-enabled medical thermometers

### Device Requirements
- **Android**: 5.0+ (API level 21+)
- **Bluetooth**: BLE (Bluetooth Low Energy) support
- **Permissions**: Location and Bluetooth access

## 📊 App Features Overview

### Core Functionality
- 🔍 **Device Discovery**: Automatic BLE scanning
- 🔗 **Smart Connection**: Robust connection management
- 📊 **Multi-Protocol**: Supports standard and custom protocols
- 💾 **Data Storage**: Local SQLite database
- 📱 **Real-time UI**: Live temperature updates

### User Interface
- **Connection Status**: Visual connection indicator
- **Current Temperature**: Large, easy-to-read display
- **Temperature History**: Scrollable list of past readings
- **Device Management**: Connect/disconnect controls

## 🛠️ Development Tips

### Adding Custom Thermometer Support

1. **Identify Data Format**
```kotlin
// Check logs for raw data format
Log.d(TAG, "Raw data: ${data.joinToString(" ") { "%02x".format(it) }}")
```

2. **Add Custom Parser**
```kotlin
// In parseTemperatureData() method
if (data.size == YOUR_SIZE && data[0] == YOUR_HEADER) {
    return parseYourDeviceData(data)
}
```

3. **Test & Validate**
- Test with actual device
- Verify temperature accuracy
- Check edge cases

### Code Structure
```
app/src/main/java/com/example/medicalpoc/
├── MainActivity.kt              # Main UI controller
├── bluetooth/
│   ├── BluetoothConnectionManager.kt  # Core BLE logic
│   └── BluetoothDeviceScanner.kt     # Device discovery
├── data/                        # Database layer
└── ui/                         # UI adapters
```

### Key Classes
- **BluetoothConnectionManager**: Handles BLE connections and data parsing
- **BluetoothDeviceScanner**: Manages device discovery
- **TemperatureRepository**: Data persistence layer
- **MainActivity**: UI coordination and user interaction

## 📞 Support

### Getting Help
1. **Check logs** using the debug commands above
2. **Review troubleshooting** section for common issues
3. **Check device compatibility** with your thermometer model
4. **Create issue** with device details and log output

### Reporting Issues
When reporting issues, please include:
- Android version
- Thermometer model/brand
- Error messages from logcat
- Steps to reproduce the issue

---

**Ready to start? Build the project and connect your thermometer! 🌡️**
