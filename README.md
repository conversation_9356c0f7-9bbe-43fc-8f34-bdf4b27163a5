# Medical Device Hub - Multi-Device Bluetooth Integration

A comprehensive Android application for integrating multiple types of Bluetooth-enabled medical devices including thermometers, pulse oximeters, blood pressure monitors, and more. Features robust device management, multi-protocol support, data storage, and adaptive user interface components.

## 🏗️ Architecture Overview

This project follows **MVVM (Model-View-ViewModel)** architecture with **Repository Pattern** for clean separation of concerns:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MainActivity  │────│ BluetoothManager │────│ TemperatureRepo │
│   (View Layer)  │    │  (Business Logic)│    │ (Data Layer)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         │                        │                        │
    ┌────▼────┐              ┌────▼────┐              ┌────▼────┐
    │   UI    │              │ Scanner │              │ Database│
    │Adapters │              │ Manager │              │  (Room) │
    └─────────┘              └─────────┘              └─────────┘
```

## 📱 Key Features

### Core Functionality
- **🏥 Multi-Device Support**: 8 different medical device types with specialized protocols
- **🔍 Smart Device Discovery**: Device-type filtered Bluetooth scanning with service UUID matching
- **🔗 Robust Connection Management**: Handles connection states, reconnection, and error recovery
- **📊 Real-time Medical Monitoring**: Live readings with device-specific measurements and units
- **💾 Unified Data Storage**: SQLite database with Room ORM supporting all device types
- **📱 Adaptive User Interface**: Dynamic UI that changes based on selected device type
- **🔄 Background Processing**: Coroutines for non-blocking operations
- **⚡ Multi-Protocol Support**: Standard medical profiles + custom device protocols

### Supported Medical Devices
1. **🌡️ Thermometer** - Body temperature measurement (°C/°F)
2. **🫀 Pulse Oximeter** - Blood oxygen saturation (SpO2) and heart rate
3. **🩸 HbCheck Biosense** - Hemoglobin level analysis (g/dL)
4. **📊 A1 Check Analyzer** - HbA1c diabetes monitoring (%)
5. **💊 Blood Lipid Meter** - Cholesterol and lipid profile analysis (mg/dL)
6. **🩺 Digital Stethoscope** - Heart rate and rhythm analysis (BPM)
7. **💓 Blood Pressure Monitor** - Systolic/diastolic pressure (mmHg)
8. **🫁 Spirometer** - Lung function testing (L, FEV1, FVC)

## 🛠️ Technology Stack

- **Language**: Kotlin
- **UI Framework**: Android Views with Material Design
- **Database**: Room (SQLite)
- **Async Programming**: Kotlin Coroutines + StateFlow
- **Bluetooth**: Android Bluetooth Low Energy (BLE) API
- **Architecture**: MVVM + Repository Pattern
- **Build System**: Gradle with Kotlin DSL

## 📂 Project Structure

```
app/src/main/java/com/example/medicalpoc/
├── MainActivity.kt                    # Main UI controller
├── bluetooth/
│   ├── BluetoothConnectionManager.kt  # BLE connection & data parsing
│   └── BluetoothDeviceScanner.kt     # Device discovery & scanning
├── data/
│   ├── TemperatureEntity.kt          # Room database entity
│   ├── TemperatureDao.kt             # Database access object
│   ├── TemperatureDatabase.kt        # Room database configuration
│   └── TemperatureRepository.kt      # Data repository pattern
└── ui/
    ├── BluetoothDeviceAdapter.kt     # Device list RecyclerView adapter
    └── TemperatureHistoryAdapter.kt  # Temperature history adapter
```

## 🔧 Core Components Explained

### 1. BluetoothConnectionManager.kt

The heart of the BLE communication system. This class handles:

#### **Connection Management**
```kotlin
private val gattCallback = object : BluetoothGattCallback() {
    override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
        when (newState) {
            BluetoothProfile.STATE_CONNECTED -> {
                _connectionState.value = ConnectionState.CONNECTED
                gatt?.discoverServices() // Discover available services
            }
            BluetoothProfile.STATE_DISCONNECTED -> {
                _connectionState.value = ConnectionState.DISCONNECTED
                bluetoothGatt?.close()
            }
        }
    }
}
```

#### **Service Discovery & Notification Setup**
```kotlin
override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
    // Log all services and characteristics for debugging
    gatt?.services?.forEach { service ->
        Log.d(TAG, "Service found: ${service.uuid}")
        service.characteristics.forEach { characteristic ->
            Log.d(TAG, "  Characteristic: ${characteristic.uuid}")
        }
    }
    setupNotifications(gatt) // Enable temperature notifications
}
```

#### **Advanced Temperature Data Parsing**

The app supports multiple thermometer protocols through intelligent parsing:

**1. Custom Protocol Detection**
```kotlin
private fun parseTemperatureData(data: ByteArray): TemperatureReading {
    return if (data.size == 5 && data[0].toInt() and 0xFF == 0xAA) {
        parseCustomThermometerData(data) // Custom format (AA XX XX XX XX)
    } else {
        // Try standard protocols
        when {
            data.size >= 5 -> parseStandardHealthThermometerData(data)
            data.size >= 4 -> parseSimpleFloatData(data)
            data.size >= 2 -> parseSimpleIntegerData(data)
        }
    }
}
```

**2. Multi-Method Custom Parsing**
```kotlin
private fun parseCustomThermometerData(data: ByteArray): TemperatureReading {
    val candidates = mutableListOf<Pair<Float, String>>()
    
    // Method 1: Last 2 bytes (little endian)
    val raw1 = (data[3].toInt() and 0xFF) or ((data[4].toInt() and 0xFF) shl 8)
    candidates.add(Pair(raw1 / 100.0f, "Last 2 bytes LE /100"))
    
    // Method 2: Big endian interpretation
    val raw2 = (data[4].toInt() and 0xFF) or ((data[3].toInt() and 0xFF) shl 8)
    candidates.add(Pair(raw2 / 100.0f, "Last 2 bytes BE /100"))
    
    // ... 20+ more parsing methods
    
    // Filter reasonable temperatures (20-50°C for medical use)
    val reasonableCandidates = candidates.filter { it.first in 20.0f..50.0f }
    
    return TemperatureReading(
        temperature = reasonableCandidates.firstOrNull()?.first ?: candidates.first().first,
        unit = "C",
        timestamp = System.currentTimeMillis()
    )
}
```

### 2. BluetoothDeviceScanner.kt

Handles BLE device discovery with modern Android permissions:

```kotlin
class BluetoothDeviceScanner(private val context: Context) {
    private val _discoveredDevices = MutableStateFlow<List<BluetoothDevice>>(emptyList())
    val discoveredDevices: StateFlow<List<BluetoothDevice>> = _discoveredDevices.asStateFlow()
    
    private val scanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult?) {
            result?.device?.let { device ->
                if (!_discoveredDevices.value.contains(device)) {
                    _discoveredDevices.value = _discoveredDevices.value + device
                }
            }
        }
    }
}
```

### 3. Data Layer (Room Database)

**Entity Definition**
```kotlin
@Entity(tableName = "temperature_readings")
data class TemperatureEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val temperature: Float,
    val unit: String,
    val timestamp: Long,
    val deviceName: String? = null
)
```

**DAO with Reactive Queries**
```kotlin
@Dao
interface TemperatureDao {
    @Query("SELECT * FROM temperature_readings ORDER BY timestamp DESC")
    fun getAllReadingsFlow(): Flow<List<TemperatureEntity>>
    
    @Insert
    suspend fun insertReading(reading: TemperatureEntity)
    
    @Query("DELETE FROM temperature_readings")
    suspend fun clearAllReadings()
}
```

**Repository Pattern**
```kotlin
class TemperatureRepository(private val temperatureDao: TemperatureDao) {
    fun getAllReadings(): Flow<List<TemperatureEntity>> = temperatureDao.getAllReadingsFlow()
    
    suspend fun insertReading(reading: TemperatureEntity) {
        temperatureDao.insertReading(reading)
    }
}
```

## 🚀 Getting Started

### Prerequisites

- Android Studio Arctic Fox or newer
- Android SDK 21+ (Android 5.0+)
- Bluetooth Low Energy capable device
- Compatible medical thermometer

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd medicalpoc
```

2. **Open in Android Studio**
```bash
# Open the project in Android Studio
# File -> Open -> Select the project directory
```

3. **Build and Run**
```bash
# Using Gradle wrapper
./gradlew assembleDebug
adb install app/build/outputs/apk/debug/app-debug.apk
```

### Required Permissions

The app automatically requests these permissions:

```xml
<!-- Bluetooth permissions for Android 12+ -->
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

<!-- Location permission for BLE scanning -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

<!-- Legacy Bluetooth permissions -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
```

## 📖 Usage Guide

### 1. **Device Connection**
1. Launch the app
2. Tap "Scan for Devices"
3. Grant Bluetooth and Location permissions
4. Select your thermometer from the discovered devices list
5. Wait for connection confirmation

### 2. **Taking Temperature Readings**
1. Ensure your thermometer is connected (green status)
2. Use your thermometer normally (forehead/ear measurement)
3. Temperature readings appear automatically in the app
4. All readings are saved to local database

### 3. **Viewing History**
- Scroll through the temperature history list
- Each entry shows temperature, timestamp, and device info
- Use "Clear History" to remove all stored readings

## 🔧 Customization

### Adding New Thermometer Protocols

To support a new thermometer model:

1. **Identify the data format** by checking logs:
```kotlin
Log.d(TAG, "Raw data: ${data.joinToString(" ") { "%02x".format(it) }}")
```

2. **Add custom parsing logic** in `parseCustomThermometerData()`:
```kotlin
// Add your device's specific parsing method
if (data.size == YOUR_DATA_SIZE && data[0] == YOUR_HEADER_BYTE) {
    return parseYourDeviceData(data)
}
```

3. **Test and validate** with your specific device

### Modifying UI

The app uses standard Android Views. Key UI files:
- `activity_main.xml` - Main layout
- `MainActivity.kt` - UI logic and event handling
- `ui/` package - RecyclerView adapters

## 🐛 Troubleshooting

### Common Issues

**1. "No devices found during scan"**
- Ensure Bluetooth is enabled
- Grant location permissions
- Put thermometer in pairing mode
- Check if device supports BLE

**2. "Connection failed"**
- Restart Bluetooth on both devices
- Clear Bluetooth cache: Settings > Apps > Bluetooth > Storage > Clear Cache
- Ensure thermometer is not connected to other devices

**3. "Infinity temperature readings"**
- This indicates parsing issues with custom protocols
- Check logcat for detailed parsing attempts
- The app will try multiple parsing methods automatically

**4. "Permission denied errors"**
- Manually grant permissions in Settings > Apps > Medical Thermometer > Permissions
- Ensure location services are enabled (required for BLE scanning)

### Debug Logging

Enable detailed logging by filtering for these tags:
```bash
adb logcat | grep -E "(BluetoothConnectionManager|BluetoothDeviceScanner|medicalpoc)"
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Android Bluetooth Low Energy documentation
- Health Thermometer Profile specification
- Room database architecture guidance
- Material Design components

## 🔬 Technical Deep Dive

### Bluetooth Low Energy (BLE) Implementation

#### Service Discovery Process
```kotlin
// 1. Connect to device
bluetoothGatt = device.connectGatt(context, false, gattCallback)

// 2. Discover services after connection
gatt?.discoverServices()

// 3. Find temperature characteristics
val service = gatt?.getService(HEALTH_THERMOMETER_SERVICE_UUID)
val characteristic = service?.getCharacteristic(TEMPERATURE_MEASUREMENT_CHAR_UUID)

// 4. Enable notifications
gatt.setCharacteristicNotification(characteristic, true)
```

#### Data Flow Architecture
```
Thermometer Device
       ↓ (BLE Notification)
BluetoothConnectionManager
       ↓ (Parse & Validate)
TemperatureReading
       ↓ (StateFlow)
MainActivity UI
       ↓ (Save to DB)
TemperatureRepository
       ↓ (Room Database)
SQLite Storage
```

### Custom Protocol Analysis

Your thermometer uses a 5-byte custom protocol:
```
Byte 0: 0xAA (Header/Sync byte)
Byte 1: 0x33 (Command/Type identifier)
Byte 2: 0x0D (Status/Mode byte)
Byte 3-4: Temperature data (16-bit value)
```

The app tries multiple interpretations:
- **Little Endian**: `(byte3) | (byte4 << 8)`
- **Big Endian**: `(byte4) | (byte3 << 8)`
- **Scaling**: `/100`, `/10`, raw values
- **Single bytes**: Individual byte interpretation

### Database Schema

```sql
CREATE TABLE temperature_readings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    temperature REAL NOT NULL,
    unit TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    deviceName TEXT
);

-- Indexes for performance
CREATE INDEX idx_timestamp ON temperature_readings(timestamp DESC);
```

### State Management

The app uses **Kotlin StateFlow** for reactive programming:

```kotlin
// Connection state
private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()

// Temperature data
private val _temperatureData = MutableStateFlow<TemperatureReading?>(null)
val temperatureData: StateFlow<TemperatureReading?> = _temperatureData.asStateFlow()

// UI observes state changes
lifecycleScope.launch {
    bluetoothConnectionManager.temperatureData.collect { reading ->
        reading?.let { updateTemperatureDisplay(it) }
    }
}
```

## 📊 Performance Considerations

### Memory Management
- **Connection cleanup**: Properly close GATT connections
- **StateFlow**: Efficient reactive updates without memory leaks
- **Database**: Room handles connection pooling automatically

### Battery Optimization
- **Scan timeout**: Automatic stop after 10 seconds
- **Connection management**: Disconnect when not needed
- **Background processing**: Minimal background work

### Threading
- **Main Thread**: UI updates only
- **IO Thread**: Database operations (Room handles this)
- **Bluetooth Thread**: BLE callbacks run on system threads

## 🧪 Testing Strategy

### Unit Testing
```kotlin
@Test
fun `parseCustomThermometerData should return correct temperature`() {
    val testData = byteArrayOf(0xAA.toByte(), 0x33, 0x0D, 0x86.toByte(), 0x70)
    val result = bluetoothConnectionManager.parseTemperatureData(testData)

    assertThat(result.temperature).isInRange(20f, 50f)
    assertThat(result.unit).isEqualTo("C")
}
```

### Integration Testing
- Test with real thermometer devices
- Verify data persistence
- Check permission handling

### UI Testing
```kotlin
@Test
fun `clicking scan button should start device discovery`() {
    onView(withId(R.id.scanButton)).perform(click())
    onView(withId(R.id.scanProgressBar)).check(matches(isDisplayed()))
}
```

## 🔐 Security Considerations

### Data Privacy
- **Local Storage**: All data stored locally, no cloud transmission
- **Encryption**: Consider encrypting sensitive health data
- **Permissions**: Minimal required permissions only

### Bluetooth Security
- **Pairing**: Secure pairing with compatible devices
- **Data Validation**: Validate all incoming BLE data
- **Connection Security**: Use secure BLE connections when available

## 🚀 Future Enhancements

### Planned Features
- [ ] **Multi-device support**: Connect multiple thermometers
- [ ] **Data export**: CSV/PDF export functionality
- [ ] **Cloud sync**: Optional cloud backup
- [ ] **Alerts**: Temperature threshold notifications
- [ ] **Charts**: Temperature trend visualization
- [ ] **User profiles**: Multiple user support

### Technical Improvements
- [ ] **Jetpack Compose**: Migrate to modern UI framework
- [ ] **Hilt**: Dependency injection
- [ ] **DataStore**: Replace SharedPreferences
- [ ] **WorkManager**: Background sync tasks

---

**Built with ❤️ for medical device integration**
