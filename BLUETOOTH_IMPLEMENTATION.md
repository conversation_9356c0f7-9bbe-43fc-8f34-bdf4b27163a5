# Bluetooth Implementation Deep Dive

## Overview

The `BluetoothConnectionManager` is the core component responsible for managing Bluetooth Low Energy (BLE) connections with medical thermometer devices. It handles device connection, service discovery, data parsing, and error management.

## Class Architecture

### Core Components

```kotlin
class BluetoothConnectionManager(private val context: Context) {
    // GATT connection instance
    private var bluetoothGatt: BluetoothGatt? = null
    
    // Reactive state management
    private val _connectionState = MutableStateFlow(ConnectionState.DISCONNECTED)
    private val _temperatureData = MutableStateFlow<TemperatureReading?>(null)
    private val _connectionError = MutableStateFlow<String?>(null)
    
    // Public state flows for UI observation
    val connectionState: StateFlow<ConnectionState> = _connectionState.asStateFlow()
    val temperatureData: StateFlow<TemperatureReading?> = _temperatureData.asStateFlow()
    val connectionError: StateFlow<String?> = _connectionError.asStateFlow()
}
```

### State Management Pattern

The class uses **StateFlow** for reactive state management, allowing the UI to observe changes without tight coupling:

- **ConnectionState**: Tracks connection status (DISCONNECTED, CONNECTING, CONNECTED)
- **TemperatureReading**: Emits new temperature data when received
- **ConnectionError**: Provides error messages for user feedback

## Connection Lifecycle

### 1. Device Connection

```kotlin
fun connectToDevice(device: BluetoothDevice) {
    // Permission check
    if (!hasBluetoothPermissions()) {
        _connectionError.value = "Bluetooth permissions not granted"
        return
    }
    
    // Initiate GATT connection
    bluetoothGatt = device.connectGatt(context, false, gattCallback)
}
```

**Key Points:**
- Checks permissions before attempting connection
- Uses `connectGatt()` with `autoConnect = false` for immediate connection
- Stores GATT instance for future operations

### 2. GATT Callback Handling

```kotlin
private val gattCallback = object : BluetoothGattCallback() {
    override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
        when (newState) {
            BluetoothProfile.STATE_CONNECTED -> {
                _connectionState.value = ConnectionState.CONNECTED
                gatt?.discoverServices() // Trigger service discovery
            }
            BluetoothProfile.STATE_DISCONNECTED -> {
                _connectionState.value = ConnectionState.DISCONNECTED
                cleanup() // Clean up resources
            }
        }
    }
}
```

**Connection States:**
- **CONNECTING**: Initial connection attempt
- **CONNECTED**: Successfully connected, triggers service discovery
- **DISCONNECTED**: Connection lost or terminated

### 3. Service Discovery

```kotlin
override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
    if (status == BluetoothGatt.GATT_SUCCESS) {
        // Log all available services for debugging
        gatt?.services?.forEach { service ->
            Log.d(TAG, "Service found: ${service.uuid}")
            service.characteristics.forEach { characteristic ->
                Log.d(TAG, "  Characteristic: ${characteristic.uuid}, Properties: ${characteristic.properties}")
            }
        }
        
        setupNotifications(gatt) // Enable temperature notifications
    }
}
```

**Service Discovery Process:**
1. Enumerate all available services
2. Log service and characteristic UUIDs for debugging
3. Attempt to setup notifications on temperature characteristics

## Notification Setup Strategy

### Multi-Protocol Support

The app uses a **fallback strategy** to support various thermometer protocols:

```kotlin
private fun setupNotifications(gatt: BluetoothGatt?) {
    var notificationSetup = false

    // Strategy 1: Try standard Health Thermometer Service
    val standardService = gatt?.getService(HEALTH_THERMOMETER_SERVICE_UUID)
    val standardCharacteristic = standardService?.getCharacteristic(TEMPERATURE_MEASUREMENT_CHAR_UUID)
    
    if (standardCharacteristic != null) {
        notificationSetup = enableNotificationForCharacteristic(gatt, standardCharacteristic)
    }

    // Strategy 2: Try all available characteristics with notification capability
    if (!notificationSetup) {
        gatt?.services?.forEach { service ->
            service.characteristics.forEach { char ->
                val properties = char.properties
                if ((properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0) {
                    enableNotificationForCharacteristic(gatt, char)
                }
            }
        }
    }
}
```

### Notification Enablement

```kotlin
private fun enableNotificationForCharacteristic(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic): Boolean {
    // Enable notifications on the characteristic
    val success = gatt.setCharacteristicNotification(characteristic, true)
    
    // Write to Client Characteristic Configuration Descriptor
    val descriptor = characteristic.getDescriptor(CLIENT_CHARACTERISTIC_CONFIG_UUID)
    descriptor?.let {
        val value = if ((characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
            BluetoothGattDescriptor.ENABLE_INDICATION_VALUE
        } else {
            BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
        }
        it.value = value
        return gatt.writeDescriptor(it)
    }
    
    return success
}
```

**Notification Types:**
- **NOTIFY**: Simple notifications (most common)
- **INDICATE**: Notifications with acknowledgment (more reliable)

## Temperature Data Parsing

### Multi-Method Parsing Strategy

The app implements a sophisticated parsing system to handle various thermometer protocols:

```kotlin
private fun parseTemperatureData(data: ByteArray): TemperatureReading {
    return if (data.size == 5 && data[0].toInt() and 0xFF == 0xAA) {
        parseCustomThermometerData(data) // Custom protocol
    } else {
        // Standard protocols
        when {
            data.size >= 5 -> parseStandardHealthThermometerData(data)
            data.size >= 4 -> parseSimpleFloatData(data)
            data.size >= 2 -> parseSimpleIntegerData(data)
            else -> throw IllegalArgumentException("Data too short")
        }
    }
}
```

### Custom Protocol Parsing

For your specific thermometer (5-byte format starting with 0xAA):

```kotlin
private fun parseCustomThermometerData(data: ByteArray): TemperatureReading {
    val candidates = mutableListOf<Pair<Float, String>>()
    
    // Method 1: Last 2 bytes (Little Endian)
    val raw1 = (data[3].toInt() and 0xFF) or ((data[4].toInt() and 0xFF) shl 8)
    candidates.add(Pair(raw1 / 100.0f, "Last 2 bytes LE /100: $raw1"))
    candidates.add(Pair(raw1 / 10.0f, "Last 2 bytes LE /10: $raw1"))
    
    // Method 2: Last 2 bytes (Big Endian)
    val raw2 = (data[4].toInt() and 0xFF) or ((data[3].toInt() and 0xFF) shl 8)
    candidates.add(Pair(raw2 / 100.0f, "Last 2 bytes BE /100: $raw2"))
    
    // Method 3: Individual bytes
    for (i in 1..4) {
        val singleByte = data[i].toInt() and 0xFF
        candidates.add(Pair(singleByte.toFloat(), "Byte $i raw: $singleByte"))
    }
    
    // Filter reasonable temperatures (20-50°C for medical use)
    val reasonableCandidates = candidates.filter { it.first in 20.0f..50.0f }
    
    val finalTemp = reasonableCandidates.firstOrNull()?.first ?: candidates.first().first
    
    return TemperatureReading(
        temperature = finalTemp,
        unit = "C",
        timestamp = System.currentTimeMillis()
    )
}
```

**Parsing Methods:**
1. **Little Endian 16-bit**: `(byte3) | (byte4 << 8)`
2. **Big Endian 16-bit**: `(byte4) | (byte3 << 8)`
3. **Scaled values**: Division by 100, 10, or raw
4. **Individual bytes**: Single byte interpretation
5. **Signed values**: Signed integer interpretation

### Standard Health Thermometer Protocol

```kotlin
private fun parseStandardHealthThermometerData(data: ByteArray): TemperatureReading {
    val flags = data[0].toInt()
    val temperatureUnit = if ((flags and 0x01) != 0) "F" else "C"
    
    // IEEE-11073 32-bit FLOAT format
    val mantissa = ((data[3].toInt() and 0xFF) shl 16) or
                  ((data[2].toInt() and 0xFF) shl 8) or
                  (data[1].toInt() and 0xFF)
    val exponent = data[4].toInt()
    
    val temperature = mantissa * Math.pow(10.0, exponent.toDouble()).toFloat()
    
    return TemperatureReading(temperature, temperatureUnit, System.currentTimeMillis())
}
```

**IEEE-11073 Format:**
- **Flags byte**: Temperature unit and other settings
- **Mantissa**: 24-bit temperature value
- **Exponent**: 8-bit scaling factor
- **Formula**: `temperature = mantissa × 10^exponent`

## Error Handling

### Permission Management

```kotlin
private fun hasBluetoothPermissions(): Boolean {
    return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
        // Android 12+ requires BLUETOOTH_CONNECT
        ActivityCompat.checkSelfPermission(context, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
    } else {
        // Older versions require FINE_LOCATION for BLE scanning
        ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
    }
}
```

### Connection Error Handling

```kotlin
override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
    if (status != BluetoothGatt.GATT_SUCCESS) {
        _connectionError.value = "Connection failed with status: $status"
        _connectionState.value = ConnectionState.DISCONNECTED
        return
    }
    // ... handle successful state changes
}
```

### Data Validation

```kotlin
private fun parseTemperatureData(data: ByteArray): TemperatureReading {
    if (data.isEmpty()) {
        throw IllegalArgumentException("Empty data array")
    }
    
    // Validate temperature range
    val temperature = /* parsing logic */
    if (temperature < -50 || temperature > 100) {
        Log.w(TAG, "Temperature out of reasonable range: $temperature")
    }
    
    return TemperatureReading(temperature, unit, timestamp)
}
```

## Resource Management

### Connection Cleanup

```kotlin
fun disconnect() {
    try {
        bluetoothGatt?.disconnect()
        bluetoothGatt?.close()
        bluetoothGatt = null
    } catch (e: SecurityException) {
        _connectionError.value = "Security exception during disconnection: ${e.message}"
    }
}
```

### Memory Management

- **GATT Connection**: Properly closed to prevent memory leaks
- **StateFlow**: Automatically handles subscriber cleanup
- **Callback References**: No strong references to prevent memory leaks

## Usage Example

```kotlin
// Initialize
val connectionManager = BluetoothConnectionManager(context)

// Observe connection state
lifecycleScope.launch {
    connectionManager.connectionState.collect { state ->
        when (state) {
            ConnectionState.CONNECTED -> showConnectedUI()
            ConnectionState.CONNECTING -> showConnectingUI()
            ConnectionState.DISCONNECTED -> showDisconnectedUI()
        }
    }
}

// Observe temperature data
lifecycleScope.launch {
    connectionManager.temperatureData.collect { reading ->
        reading?.let { displayTemperature(it) }
    }
}

// Connect to device
connectionManager.connectToDevice(selectedDevice)
```

This implementation provides a robust, flexible foundation for integrating various Bluetooth medical thermometers with comprehensive error handling and protocol support.
